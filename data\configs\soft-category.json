﻿[
  {
    "os": "Windows",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table1-Easv4-Windows-N3",
      "#vm-table1-Eav4-Windows-N3",
      "#vm-table1-Dasv5-Windows-N3",
      "#vm-table1-Dadsv5-Windows-N3",
      "#vm-table1-Dav4-Windows-N3",
      "#vm-table1-Dasv4-Windows-N3",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-NCas_T4_v3-Windows-N3-CPP",
      "#vm-table1-Eadsv5-Windows-N3",
      "#vm-table1-Easv5-Windows-N2-E2-N3",
      "#vm-table1-NCsv3-Windows-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3",
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-r2-9-east3",
      "#vm-table-mdsv2-w-n3",
      "#vm-table-mdsv2-w-n3-1",
      "#vm-table-mdsv2-w-n3-2",
      "#vm-table1-r2-9-north3",
      "#vm-table1-r2-1-east3",
      "#vm-table1-r2-2-east3",
      "#vm-table1-1-3-east3",
      "#vm-table1-r2-3-east3",
      "#vm-table1-r2-4-east3",
      "#vm-table1-1-6-east3",
      "#vm-table2-1-new-east3",
      "#vm-table1-2-new-east3",
      "#vm-table2-4-new-east3",
      "#vm-table1-3-new-east3",
      "#vm-table1-4-new-east3",
      "#vm-table1-r2-5-east3",
      "#vm-table1-r2-7-east3",
      "#vm-table1-r2-8-east3",
      "#vm-table1-3-1-east3",
      "#vm-table1-3-2-east3",
      "#vm-table1-5-new-east3",
      "#vm-table1-6-new-east3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-7-new-east3",
      "#vm-table1-8-new-east3",
      "#vm-table-Constrained-1-1-east3",
      "#vm-windows-computingoptimization-fsv2-east3",
      "#vm-table1-r2-1-north3",
      "#vm-table1-r2-2-north3",
      "#vm-table1-1-3-north3",
      "#vm-table1-r2-3-north3",
      "#vm-table1-r2-4-north3",
      "#vm-table1-1-6-north3",
      "#vm-table2-1-new-north3",
      "#vm-table1-2-new-north3",
      "#vm-table2-4-new-north3",
      "#vm-table1-3-new-north3",
      "#vm-table1-4-new-north3",
      "#vm-table1-r2-5-north3",
      "#vm-windows-computingoptimization-fsv2-north3",
      "#vm-table1-r2-7-north3",
      "#vm-table1-r2-8-north3",
      "#vm-table1-3-1-north3",
      "#vm-table1-3-2-north3",
      "#vm-table1-5-new-north3",
      "#vm-table1-6-new-north3",
      "#vm-table1-7-new-north3",
      "#vm-table1-8-new-north3",
      "#vm-table-Constrained-1-1-north3",
      "#vm-table1-11-cpp-new-east3",
      "#vm-table1-11-cpp-new-north3",
      "#vm-table1-1-7",
      "#vm-table1-3-3",
      "#vm-table1-3-1",
      "#vm-table1-r2-4",
      "#vm-table-windows-memoryprioritization-d15v2",
      "#vm-table-windows-memoryprioritization-ds15v2",
      "#vms-table1-1-7",
      "#vms-table1-3-3",
      "#vms-table1-3-1",
      "#vms-table-windows-memoryprioritization-d15v2",
      "#vms-table-windows-memoryprioritization-ds15v2",
      "#vm-table1-1-1-ml",
      "#vm-table1-1-2-ml",
      "#vm-table1-1-3-ml",
      "#vm-table1-1-4-ml",
      "#vm-table1-1-5-ml",
      "#vm-table1-1-6-ml",
      "#vm-table1-1-7-ml",
      "#vm-table1-1-8-ml",
      "#vm-table1-1-9-ml",
      "#vm-windows-computingoptimization-fsv2-ml",
      "#vm-table-windows-computingprioritization-f1-f16-ml",
      "#vm-table1-2-2-ml",
      "#vm-table1-3-1-ml",
      "#vm-windows-memoryprioritization-ev3-region2-ml",
      "#vm-table1-3-2-ml",
      "#vm-table1-3-3-ml",
      "#vm-table1-3-4-ml",
      "#vm-table-windows-memoryprioritization-d15v2-ml",
      "#vm-table1-3-5-ml",
      "#vm-table-windows-memoryprioritization-ds15v2-ml",
      "#vm-table1-3-6-ml",
      "#vm-table1-4-1-ml",
      "#vm-table1-r2-1",
      "#vm-table1-r2-2",
      "#vm-table1-r2-3",
      "#vm-table1-r2-4",
      "#vm-table1-r2-5",
      "#vm-table1-r2-6",
      "#vm-table1-r2-7",
      "#vm-table1-r2-8",
      "#vm-table1-r2-9",
      "#vm-table1-r2-10",
      "#vm-table1-r2-11",
      "#vm-table1-6-cpp-new-east3",
      "#vm-table1-6-cpp-new-north3",
      "#vm-table-Constrained-1-2",
      "#vm-table-Constrained-1-3",
      "#vm-table1-d2v51-n3",
      "#vm-table1-d2v51-s-n3",
      "#vm-table1-ev5-s-n3",
      "#vm-table1-ddsv5-Win-E3",
      "#vm-table1-ev5-n3",
      "#vm-table1-4-cpp-new",
      "#vm-table1-1-6"
    ]
  },
  {
    "os": "Windows",
    "region": "north-china",
    "tableIDs": [
      "#vm-table1-Easv4-Windows-N3",
      "#vm-table1-Eav4-Windows-N3",
      "#vm-table1-Dasv5-Windows-N3",
      "#vm-table1-Dadsv5-Windows-N3",
      "#vm-table1-Dav4-Windows-N3",
      "#vm-table1-Dasv4-Windows-N3",
      "#vm-table1-windows-Bsv2-north3-north2-east2",
      "#vm-table1-linux-Bsv2-north3-north2-east2",
      "#vm-table1-windows-Basv2-north3-north2-east2",
      "#vm-table1-linux-Basv2-north3-north2-east2",
      "#vm-table1-windows-B2pts-north3-north2-east2",
      "#vm-table1-linux-B2pts-north3-north2-east2",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-Dldsv5-Windows-N2-E2-N3",
      "#vm-table-Dldsv5-Linux-N2-E2-N3",
      "#vm-table-Dlsv5-Windows-N2-E2-N3",
      "#vm-table-Dlsv5-Linux-N2-E2-N3",
      "#vm-table1-Ebsv5-Windows-N3-N2-E2",
      "#vm-table1-Ebsv5-Linux-N3-N2-E2",
      "#vm-table-NCas_T4_v3-Windows-N3-CPP",
      "#vm-table1-Ebdsv5-Windows-N3-N2-E2",
      "#vm-table1-Eadsv5-Windows-N3",
      "#vm-table1-Easv5-Windows-N2-E2-N3",
      "#vm-table1-Dadsv5-Windows-N2-E2-N3",
      "#vm-table1-Dasv5-Windows-N2-E2-N3",
      "#vm-table1-Easv4-Windows-N3-N2-E2",
      "#vm-table1-Eav4-Windows-N3-N2-E2",
      "#vm-table1-Dasv4-Windows-N2-E2-N3",
      "#vm-table1-Dav4-Windows-N2-E2-N3",
      "#vm-table1-NCsv3-Windows-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3",
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-d2v51-dsen2",
      "#vm-table1-ev5-s-en2",
      "#vm-table1-ev5-ls",
      "#vm-table1-d2v51-ls",
      "#vm-table1-d2v51-s",
      "#vm-table1-ev5-s",
      "#vm-table1-d2v51",
      "#vm-table1-ev5",
      "#vm-table-mdsv2-w-n3",
      "#vm-table-mdsv2-w-n3-1",
      "#vm-table-mdsv2-w-n3-2",
      "#vm-table1-r2-9-east3",
      "#vm-table1-r2-9-north3",
      "#vm-table1-r2-1-east3",
      "#vm-table1-r2-2-east3",
      "#vm-table1-1-3-east3",
      "#vm-table1-r2-3-cpp",
      "#vm-table1-r2-3-east3",
      "#vm-table1-r2-4-east3",
      "#vm-table1-1-6-east3",
      "#vm-table2-1-new-east3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-2-new-east3",
      "#vm-table2-4-new-east3",
      "#vm-table1-3-new-east3",
      "#vm-table1-4-new-east3",
      "#vm-table1-r2-5-east3",
      "#vm-table1-r2-7-east3",
      "#vm-table1-r2-8-east3",
      "#vm-table1-3-1-east3",
      "#vm-table1-3-2-east3",
      "#vm-table1-5-new-east3",
      "#vm-table1-6-new-east3",
      "#vm-table1-7-new-east3",
      "#vm-table1-8-new-east3",
      "#vm-table-Constrained-1-1-east3",
      "#vm-windows-computingoptimization-fsv2-east3",
      "#vm-table1-r2-1-north3",
      "#vm-table1-r2-2-north3",
      "#vm-table1-1-3-north3",
      "#vm-table1-r2-3-north3",
      "#vm-table1-r2-4-north3",
      "#vm-table1-r2-4-n2e2",
      "#vm-table1-1-6-north3",
      "#vm-table1-1-6-n2e2",
      "#vm-table2-1-new-north3",
      "#vm-table1-2-new-north3",
      "#vm-table2-4-new-north3",
      "#vm-table1-3-new-north3",
      "#vm-table1-4-new-north3",
      "#vm-table1-r2-5-north3",
      "#vm-windows-computingoptimization-fsv2-north3",
      "#vm-table1-r2-7-north3",
      "#vm-table1-r2-8-north3",
      "#vm-table1-3-1-north3",
      "#vm-table1-3-2-north3",
      "#vm-table1-5-new-north3",
      "#vm-table1-6-new-north3",
      "#vm-table1-7-new-north3",
      "#vm-table1-8-new-north3",
      "#vm-table-Constrained-1-1-north3",
      "#vm-table1-1-3",
      "#vm-table1-1-5",
      "#vm-table1-1-6",
      "#vm-table1-r2-4",
      "#vm-table-windows-memoryprioritization-d15v2",
      "#vm-table-windows-memoryprioritization-ds15v2",
      "#vm-windows-memoryprioritization-ev3-region2",
      "#vm-windows-computingoptimization-fsv2",
      "#vm-table1-3-2",
      "#vm-table1-3-6",
      "#vm-table1-4-1",
      "#vms-table1-1-3",
      "#vms-table1-1-5",
      "#vms-table1-1-6",
      "#vms-table1-3-1",
      "#vms-windows-memoryprioritization-ev3-region2",
      "#vms-windows-computingoptimization-fsv2",
      "#vms-table1-3-1",
      "#vms-table1-3-2",
      "#vms-table-windows-memoryprioritization-d15v2",
      "#vms-table-windows-memoryprioritization-ds15v2",
      "#vms-table1-3-6",
      "#vms-table1-4-1",
      "#vm-table1-1-1-ml",
      "#vm-table1-1-2-ml",
      "#vm-table1-1-3-ml",
      "#vm-table1-1-4-ml",
      "#vm-table1-1-5-ml",
      "#vm-table1-1-6-ml",
      "#vm-table1-1-7-ml",
      "#vm-table1-1-8-ml",
      "#vm-table1-1-9-ml",
      "#vm-windows-computingoptimization-fsv2-ml",
      "#vm-table-windows-computingprioritization-f1-f16-ml",
      "#vm-table1-2-2-ml",
      "#vm-table1-3-1-ml",
      "#vm-windows-memoryprioritization-ev3-region2-ml",
      "#vm-table1-3-2-ml",
      "#vm-table1-3-3-ml",
      "#vm-table1-3-4-ml",
      "#vm-table-windows-memoryprioritization-d15v2-ml",
      "#vm-table1-3-5-ml",
      "#vm-table-windows-memoryprioritization-ds15v2-ml",
      "#vm-table1-3-6-ml",
      "#vm-table1-4-1-ml",
      "#vm-table1-1-new",
      "#vm-table1-2-new",
      "#vm-table1-3-new",
      "#vm-table1-4-new",
      "#vm-table1-5-new",
      "#vm-table1-6-new",
      "#vm-table1-7-new",
      "#vm-table1-8-new",
      "#vm-table1-1-cpp-new",
      "#vm-table1-2-cpp-new",
      "#vm-table1-3-cpp-new",
      "#vm-table1-4-cpp-new",
      "#vm-table1-5-cpp-new",
      "#vm-table1-6-cpp-new",
      "#vm-table1-7-cpp-new",
      "#vm-table1-8-cpp-new",
      "#vm-table1-9-cpp-new",
      "#vm-table1-10-cpp-new",
      "#vm-table1-11-cpp-new",
      "#vm-table1-12-cpp-new",
      "#vm-table-Constrained-1-1",
      "#vm-table1-11-cpp-new-east3",
      "#vm-table1-11-cpp-new-north3",
      "#vm-table1-6-cpp-new-east3",
      "#vm-table1-6-cpp-new-north3",
      "#vm-table-Constrained-1-2",
      "#vm-table1-d2v51-n3",
      "#vm-table1-d2v51-s-n3",
      "#vm-table1-ev5-n3",
      "#vm-table1-ev5-s-n3",
      "#vm-table1-d2v51-l-n3",
      "#vm-table1-ev5-l-n3",
      "#vm-table1-ev5-ls-n3",
      "#vm-table1-ddsv5-Win-E3"
    ]
  },
  {
    "os": "Windows",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table1-Easv4-Windows-N3",
      "#vm-table1-Eav4-Windows-N3",
      "#vm-table1-Dasv5-Windows-N3",
      "#vm-table1-Dadsv5-Windows-N3",
      "#vm-table1-Dav4-Windows-N3",
      "#vm-table1-Dasv4-Windows-N3",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-NCas_T4_v3-Windows-N3-CPP",
      "#vm-table1-Eadsv5-Windows-N3",
      "#vm-table1-Easv5-Windows-N2-E2-N3",
      "#vm-table1-NCsv3-Windows-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3",
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table-mdsv2-w-n3",
      "#vm-table-mdsv2-w-n3-1",
      "#vm-table-mdsv2-w-n3-2",
      "#vm-table1-r2-9-east3",
      "#vm-table1-r2-9-north3",
      "#vm-table1-r2-1-east3",
      "#vm-table1-r2-2-east3",
      "#vm-table1-1-3-east3",
      "#vm-table1-r2-3-east3",
      "#vm-table1-r2-4-east3",
      "#vm-table1-1-6-east3",
      "#vm-table2-1-new-east3",
      "#vm-table1-2-new-east3",
      "#vm-table2-4-new-east3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-3-new-east3",
      "#vm-table1-4-new-east3",
      "#vm-table1-r2-5-east3",
      "#vm-table1-r2-7-east3",
      "#vm-table1-r2-8-east3",
      "#vm-table1-3-1-east3",
      "#vm-table1-3-2-east3",
      "#vm-table1-5-new-east3",
      "#vm-table1-6-new-east3",
      "#vm-table1-7-new-east3",
      "#vm-table1-8-new-east3",
      "#vm-table-Constrained-1-1-east3",
      "#vm-windows-computingoptimization-fsv2-east3",
      "#vm-table1-r2-1-north3",
      "#vm-table1-r2-2-north3",
      "#vm-table1-1-3-north3",
      "#vm-table1-r2-3-north3",
      "#vm-table1-r2-4-north3",
      "#vm-table1-1-6-north3",
      "#vm-table2-1-new-north3",
      "#vm-table1-2-new-north3",
      "#vm-table2-4-new-north3",
      "#vm-table1-3-new-north3",
      "#vm-table1-4-new-north3",
      "#vm-table1-r2-5-north3",
      "#vm-windows-computingoptimization-fsv2-north3",
      "#vm-table1-r2-7-north3",
      "#vm-table1-r2-8-north3",
      "#vm-table1-3-1-north3",
      "#vm-table1-3-2-north3",
      "#vm-table1-11-cpp-new-east3",
      "#vm-table1-11-cpp-new-north3",
      "#vm-table1-5-new-north3",
      "#vm-table1-6-new-north3",
      "#vm-table1-7-new-north3",
      "#vm-table1-8-new-north3",
      "#vm-table-Constrained-1-1-north3",
      "#vm-table1-1-7",
      "#vm-table1-3-3",
      "#vm-table1-3-1",
      "#vm-table-windows-memoryprioritization-d15v2",
      "#vm-table-windows-memoryprioritization-ds15v2",
      "#vms-table1-1-7",
      "#vms-table1-3-3",
      "#vms-table1-3-1",
      "#vms-table-windows-memoryprioritization-d15v2",
      "#vms-table-windows-memoryprioritization-ds15v2",
      "#vm-table1-1-1-ml",
      "#vm-table1-1-2-ml",
      "#vm-table1-1-3-ml",
      "#vm-table1-1-4-ml",
      "#vm-table1-1-5-ml",
      "#vm-table1-1-6-ml",
      "#vm-table1-1-7-ml",
      "#vm-table1-1-8-ml",
      "#vm-table1-1-9-ml",
      "#vm-windows-computingoptimization-fsv2-ml",
      "#vm-table-windows-computingprioritization-f1-f16-ml",
      "#vm-table1-2-2-ml",
      "#vm-table1-3-1-ml",
      "#vm-windows-memoryprioritization-ev3-region2-ml",
      "#vm-table1-3-2-ml",
      "#vm-table1-3-3-ml",
      "#vm-table1-3-4-ml",
      "#vm-table-windows-memoryprioritization-d15v2-ml",
      "#vm-table1-3-5-ml",
      "#vm-table-windows-memoryprioritization-ds15v2-ml",
      "#vm-table1-3-6-ml",
      "#vm-table1-4-1-ml",
      "#vm-table1-r2-1",
      "#vm-table1-r2-2",
      "#vm-table1-r2-3",
      "#vm-table1-r2-4",
      "#vm-table1-r2-5",
      "#vm-table1-r2-6",
      "#vm-table1-r2-7",
      "#vm-table1-r2-8",
      "#vm-table1-r2-9",
      "#vm-table1-r2-10",
      "#vm-table1-r2-11",
      "#vm-table-Constrained-1-2",
      "#vm-table1-6-cpp-new-east3",
      "#vm-table1-6-cpp-new-north3",
      "#vm-table-Constrained-1-3",
      "#vm-table1-d2v51-n3",
      "#vm-table1-d2v51-s-n3",
      "#vm-table1-ev5-n3",
      "#vm-table1-ev5-s-n3",
      "#vm-table1-ddsv5-Win-E3",
      "#vm-table1-4-cpp-new",
      "#vm-table1-1-6"
    ]
  },
  {
    "os": "Windows",
    "region": "east-china",
    "tableIDs": [
      "#vm-table1-Easv4-Windows-N3",
      "#vm-table1-Eav4-Windows-N3",
      "#vm-table1-Dasv5-Windows-N3",
      "#vm-table1-Dadsv5-Windows-N3",
      "#vm-table1-Dav4-Windows-N3",
      "#vm-table1-Dasv4-Windows-N3",
      "#vm-table1-windows-Bsv2-north3-north2-east2",
      "#vm-table1-linux-Bsv2-north3-north2-east2",
      "#vm-table1-windows-Basv2-north3-north2-east2",
      "#vm-table1-linux-Basv2-north3-north2-east2",
      "#vm-table1-windows-B2pts-north3-north2-east2",
      "#vm-table1-linux-B2pts-north3-north2-east2",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-Dldsv5-Windows-N2-E2-N3",
      "#vm-table-Dldsv5-Linux-N2-E2-N3",
      "#vm-table-Dlsv5-Windows-N2-E2-N3",
      "#vm-table-Dlsv5-Linux-N2-E2-N3",
      "#vm-table1-Ebsv5-Windows-N3-N2-E2",
      "#vm-table1-Ebsv5-Linux-N3-N2-E2",
      "#vm-table-NCas_T4_v3-Windows-N3-CPP",
      "#vm-table1-Ebdsv5-Windows-N3-N2-E2",
      "#vm-table1-Eadsv5-Windows-N3",
      "#vm-table1-Easv5-Windows-N2-E2-N3",
      "#vm-table1-Dadsv5-Windows-N2-E2-N3",
      "#vm-table1-Dasv5-Windows-N2-E2-N3",
      "#vm-table1-Easv4-Windows-N3-N2-E2", 
      "#vm-table1-Eav4-Windows-N3-N2-E2", 
      "#vm-table1-Dasv4-Windows-N2-E2-N3",
      "#vm-table1-Dav4-Windows-N2-E2-N3",
      "#vm-table1-NCsv3-Windows-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3",
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-d2v51-dsen2",
      "#vm-table1-ev5-s-en2",
      "#vm-table1-ev5-ls",
      "#vm-table1-d2v51-s",
      "#vm-table1-d2v51-ls",
      "#vm-table1-ev5-s",
      "#vm-table1-d2v51",
      "#vm-table1-ev5",
      "#vm-table1-r2-9-east3",
      "#vm-table-mdsv2-w-n3",
      "#vm-table-mdsv2-w-n3-1",
      "#vm-table-mdsv2-w-n3-2",
      "#vm-table1-r2-9-north3",
      "#vm-table1-r2-1-east3",
      "#vm-table1-r2-2-east3",
      "#vm-table1-1-3-east3",
      "#vm-table1-r2-3-east3",
      "#vm-table1-r2-4-east3",
      "#vm-table1-1-6-east3",
      "#vm-table1-r2-3-cpp",
      "#vm-table2-1-new-east3",
      "#vm-table1-2-new-east3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table2-4-new-east3",
      "#vm-table1-3-new-east3",
      "#vm-table1-4-new-east3",
      "#vm-table1-r2-5-east3",
      "#vm-table1-11-cpp-new-east3",
      "#vm-table1-11-cpp-new-north3",
      "#vm-table1-r2-7-east3",
      "#vm-table1-r2-8-east3",
      "#vm-table1-3-1-east3",
      "#vm-table1-3-2-east3",
      "#vm-table1-5-new-east3",
      "#vm-table1-6-new-east3",
      "#vm-table1-7-new-east3",
      "#vm-table1-8-new-east3",
      "#vm-table-Constrained-1-1-east3",
      "#vm-windows-computingoptimization-fsv2-east3",
      "#vm-table1-r2-1-north3",
      "#vm-table1-r2-2-north3",
      "#vm-table1-1-3-north3",
      "#vm-table1-r2-3-north3",
      "#vm-table1-r2-4-north3",
      "#vm-table1-r2-4-n2e2",
      "#vm-table1-1-6-north3",
      "#vm-table1-1-6-n2e2",
      "#vm-table2-1-new-north3",
      "#vm-table1-2-new-north3",
      "#vm-table2-4-new-north3",
      "#vm-table1-3-new-north3",
      "#vm-table1-4-new-north3",
      "#vm-table1-r2-5-north3",
      "#vm-windows-computingoptimization-fsv2-north3",
      "#vm-table1-r2-7-north3",
      "#vm-table1-r2-8-north3",
      "#vm-table1-3-1-north3",
      "#vm-table1-3-2-north3",
      "#vm-table1-5-new-north3",
      "#vm-table1-6-new-north3",
      "#vm-table1-7-new-north3",
      "#vm-table1-8-new-north3",
      "#vm-table-Constrained-1-1-north3",
      "#vm-table1-1-3",
      "#vm-table1-6-cpp-new-east3",
      "#vm-table1-6-cpp-new-north3",
      "#vm-table1-1-6",
      "#vm-windows-computingoptimization-fsv2",
      "#vm-windows-memoryprioritization-ev3-region2",
      "#vm-table1-3-2",
      "#vm-table-windows-memoryprioritization-d15v2",
      "#vm-table-windows-memoryprioritization-ds15v2",
      "#vm-table1-3-6",
      "#vm-table1-4-1",
      "#vms-table1-1-3",
      "#vms-table1-1-6",
      "#vms-windows-computingoptimization-fsv2",
      "#vms-windows-memoryprioritization-ev3-region2",
      "#vms-table1-3-2",
      "#vms-table-windows-memoryprioritization-d15v2",
      "#vms-table-windows-memoryprioritization-ds15v2",
      "#vms-table1-3-6",
      "#vms-table1-4-1",
      "#vm-table1-1-1-ml",
      "#vm-table1-1-2-ml",
      "#vm-table1-1-3-ml",
      "#vm-table1-1-4-ml",
      "#vm-table1-1-5-ml",
      "#vm-table1-1-6-ml",
      "#vm-table1-1-7-ml",
      "#vm-table1-1-8-ml",
      "#vm-table1-1-9-ml",
      "#vm-windows-computingoptimization-fsv2-ml",
      "#vm-table-windows-computingprioritization-f1-f16-ml",
      "#vm-table1-2-2-ml",
      "#vm-table1-3-1-ml",
      "#vm-windows-memoryprioritization-ev3-region2-ml",
      "#vm-table1-3-2-ml",
      "#vm-table1-3-3-ml",
      "#vm-table1-3-4-ml",
      "#vm-table-windows-memoryprioritization-d15v2-ml",
      "#vm-table1-3-5-ml",
      "#vm-table-windows-memoryprioritization-ds15v2-ml",
      "#vm-table1-3-6-ml",
      "#vm-table1-4-1-ml",
      "#vm-table1-1-new",
      "#vm-table1-2-new",
      "#vm-table1-3-new",
      "#vm-table1-4-new",
      "#vm-table1-5-new",
      "#vm-table1-6-new",
      "#vm-table1-7-new",
      "#vm-table1-8-new",
      "#vm-table1-1-cpp-new",
      "#vm-table1-2-cpp-new",
      "#vm-table1-3-cpp-new",
      "#vm-table1-4-cpp-new",
      "#vm-table1-5-cpp-new",
      "#vm-table1-6-cpp-new",
      "#vm-table1-7-cpp-new",
      "#vm-table1-8-cpp-new",
      "#vm-table1-9-cpp-new",
      "#vm-table1-10-cpp-new",
      "#vm-table1-11-cpp-new",
      "#vm-table1-12-cpp-new",
      "#vm-table-Constrained-1-1",
      "#vm-table-Constrained-1-3",
      "#vm-table1-d2v51-n3",
      "#vm-table1-d2v51-s-n3",
      "#vm-table1-ev5-n3",
      "#vm-table1-ev5-s-n3",
      "#vm-table1-ddsv5-Win-E3"
    ]
  },
  {
    "os": "Windows",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table1-Easv4-Windows-N3-N2-E2",
      "#vm-table1-Eav4-Windows-N3-N2-E2",
      "#vm-table1-Dasv5-Windows-N2-E2-N3",
      "#vm-table1-Dadsv5-Windows-N2-E2-N3",
      "#vm-table1-Dav4-Windows-N2-E2-N3",
      "#vm-table1-Dasv4-Windows-N2-E2-N3",
      "#vm-table1-r2-9-east3",
      "#vm-table1-r2-1-east3",
      "#vm-table1-r2-2-east3",
      "#vm-table1-1-3-east3",
      "#vm-table1-6-cpp-new-east3",
      "#vm-table1-r2-3-east3",
      "#vm-table1-r2-4-east3",
      "#vm-table1-1-6-east3",
      "#vm-table2-1-new-east3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-2-new-east3",
      "#vm-table2-4-new-east3",
      "#vm-table1-3-new-east3",
      "#vm-table1-4-new-east3",
      "#vm-table1-r2-5-east3",
      "#vm-table1-r2-7-east3",
      "#vm-table1-r2-8-east3",
      "#vm-table1-r2-1",
      "#vm-table1-3-1-east3",
      "#vm-table1-3-2-east3",
      "#vm-table1-5-new-east3",
      "#vm-table1-11-cpp-new-east3",
      "#vm-table1-6-new-east3",
      "#vm-table1-7-new-east3",
      "#vm-table1-8-new-east3",
      "#vm-table-Constrained-1-1-east3",
      "#vm-windows-computingoptimization-fsv2-east3",
      "#vm-table1-r2-1",
      "#vm-table1-1-cpp-new",
      "#vm-table1-7-new",
      "#vm-table1-r2-2",
      "#vm-table1-2-cpp-new",
      "#vm-table1-1-3",
      "#vm-table1-r2-3",
      "#vm-table1-r2-3-cpp",
      "#vm-table1-r2-4",
      "#vm-table1-4-cpp-new",
      "#vm-table1-1-6",
      "#vm-table1-1-new",
      "#vm-table1-2-new",
      "#vm-table1-3-new",
      "#vm-table1-4-new",
      "#vm-table1-r2-5",
      "#vm-table1-5-cpp-new",
      "#vm-windows-computingoptimization-fsv2",
      "#vm-table1-r2-7",
      "#vm-table1-7-cpp-new",
      "#vm-table1-r2-8",
      "#vm-table1-8-cpp-new",
      "#vm-table1-3-1",
      "#vm-windows-memoryprioritization-ev3-region2",
      "#vm-table1-r2-6",
      "#vm-table1-6-cpp-new",
      "#vm-table1-3-2",
      "#vm-table1-5-new",
      "#vm-table1-6-new",
      "#vm-table1-8-new",
      "#vm-table1-3-3",
      "#vm-table1-r2-9",
      "#vm-table1-9-cpp-new",
      "#vm-table1-r2-10",
      "#vm-table1-11-cpp-new",
      "#vm-table-Constrained-1-1",
      "#vm-table-Constrained-1-2",
      "#vm-table-Constrained-1-3",
      "#vm-table1-4-1",
      "#vm-table1-d2v51-n3",
      "#vm-table1-d2v51-s-n3",
      "#vm-table1-ev5",
      "#vm-table1-ev5-s",
      "#vm-table1-ddsv5-Win-E3",
      "#vm-table1-ddsv5-Linux-E3",
      "#vm-table1-d2dv5-Linux-N3E3",
      "#vm-table1-r2-4-n2e2",
      "#vm-table1-1-6-n2e2"
    ]
  },
  {
    "os": "Windows",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table1-windows-Bsv2-north3-north2-east2",
      "#vm-table1-linux-Bsv2-north3-north2-east2",
      "#vm-table1-windows-Basv2-north3-north2-east2",
      "#vm-table1-linux-Basv2-north3-north2-east2",
      "#vm-table1-windows-B2pts-north3-north2-east2",
      "#vm-table1-linux-B2pts-north3-north2-east2",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-Dldsv5-Windows-N2-E2-N3",
      "#vm-table-Dldsv5-Linux-N2-E2-N3",
      "#vm-table-Dlsv5-Windows-N2-E2-N3",
      "#vm-table-Dlsv5-Linux-N2-E2-N3",
      "#vm-table1-Ebsv5-Windows-N3-N2-E2",
      "#vm-table1-Ebsv5-Linux-N3-N2-E2",
      "#vm-table-NCas_T4_v3-Windows-N3-CPP",
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-r2-9-north3",
      "#vm-table-mdsv2-w-n3",
      "#vm-table-mdsv2-w-n3-1",
      "#vm-table-mdsv2-w-n3-2",
      "#vm-table1-r2-1-north3",
      "#vm-table1-r2-2-north3",
      "#vm-table1-r2-3-north3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table1-r2-4-north3",
      "#vm-table1-r2-4-n2e2",
      "#vm-table1-1-6-north3",
      "#vm-table1-1-6-n2e2",
      "#vm-table2-1-new-north3",
      "#vm-table1-2-new-north3",
      "#vm-table1-4-new-north3",
      "#vm-table1-11-cpp-new-north3",
      "#vm-table1-6-cpp-new-north3",
      "#vm-table1-r2-5-north3",
      "#vm-windows-computingoptimization-fsv2-north3",
      "#vm-table1-r2-7-north3",
      "#vm-table1-r2-8-north3",
      "#vm-table1-3-1-north3",
      "#vm-table1-3-2-north3",
      "#vm-table1-5-new-north3",
      "#vm-table1-6-new-north3",
      "#vm-table1-7-new-north3",
      "#vm-table1-8-new-north3",
      "#vm-table-Constrained-1-1-north3",
      "#vm-table1-r2-1",
      "#vm-table1-1-cpp-new",
      "#vm-table1-r2-2",
      "#vm-table1-7-new",
      "#vm-table1-2-cpp-new",
      "#vm-table1-1-3",
      "#vm-table1-r2-3",
      "#vm-table1-r2-3-cpp",
      "#vm-table1-r2-4",
      "#vm-table1-4-cpp-new",
      "#vm-table1-1-6",
      "#vm-table1-1-new",
      "#vm-table1-2-new",
      "#vm-table1-3-new",
      "#vm-table1-4-new",
      "#vm-table1-r2-5",
      "#vm-table1-5-cpp-new",
      "#vm-windows-computingoptimization-fsv2",
      "#vm-table1-r2-7",
      "#vm-table1-7-cpp-new",
      "#vm-table1-r2-8",
      "#vm-table1-8-cpp-new",
      "#vm-table1-3-1",
      "#vm-windows-memoryprioritization-ev3-region2",
      "#vm-table1-r2-6",
      "#vm-table1-6-cpp-new",
      "#vm-table1-3-2",
      "#vm-table1-5-new",
      "#vm-table1-6-new",
      "#vm-table1-8-new",
      "#vm-table1-3-3",
      "#vm-table1-r2-9",
      "#vm-table1-9-cpp-new",
      "#vm-table1-r2-10",
      "#vm-table1-11-cpp-new",
      "#vm-table1-3-6",
      "#vm-table-Constrained-1-1",
      "#vm-table-Constrained-1-2",
      "#vm-table-Constrained-1-3",
      "#vm-table1-4-1",
      "#vm-table1-Dav4-Windows-N3",
      "#vm-table1-Dadsv5-Windows-N3",
      "#vm-table1-Dasv5-Windows-N3",
      "#vm-table1-d2dv5-Linux-N3E3",
      "#vm-table1-ddsv5-Linux-E3",
      "#vm-table2-4-new-east3",
      "#vm-table1-3-new-east3",
      "#vm-table1-ev5",
      "#vm-table1-ev5-s",
      "#vm-table1-ev5-n3"
      
    ]
  },
  {
    "os": "Linux",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table1-Easv4-Linux-N3",
      "#vm-table1-Eav4-Linux-N3",
      "#vm-table1-Dasv5-Linux-N3",
      "#vm-table1-Dadsv5-Linux-N3",
      "#vm-table1-Dav4-Linux-N3",
      "#vm-table1-Dasv4-Linux-N3",  
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-NCas_T4_v3-Linux-N3-CPP",
      "#vm-table1-Eadsv5-Linux-N3",
      "#vm-table1-Easv5-Linux-N2-E2-N3",
      "#vm-table1-NCsv3-Linux-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3", 
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-linux-r2-9-east3",
      "#vm-table1-linux-r2-9-north3",
      "#vm-table-mdsv2-l-n3",
      "#vm-table-mdsv2-l-n3-1",
      "#vm-table-mdsv2-l-n3-2",
      "#vm-table1-linux-r2-11-north3",
      "#vm-table1-linux-r2-11-east3",
      "#vm-table1-linux-r2-10-east3",
      "#vm-table2-6-cpp-new-north3",
      "#vm-table1-linux-r2-1-north3",
      "#vm-table1-linux-r2-2-north3",
      "#vm-table1-linux-1-3-north3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-linux-r2-3-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table1-linux-1-6-north3",
      "#vm-table2-linux-1-new-north3",
      "#vm-table1-linux-2-new-north3",
      "#vm-table2-linux-4-new-north3",
      "#vm-table1-linux-3-new-north3",
      "#vm-table1-linux-4-new-north3",
      "#vm-table1-linux-r2-5-north3",
      "#vm-table1-linux-r2-7-north3",
      "#vm-table1-linux-r2-8-north3",
      "#vm-table1-linux-3-1-north3",
      "#vm-table1-linux-3-2-north3",
      "#vm-table1-linux-5-new-north3",
      "#vm-table1-linux-6-new-north3",
      "#vm-table1-linux-7-new-north3",
      "#vm-table1-linux-8-new-north3",
      "#vm-linux-computingoptimization-fsv2-north3",
      "#vm-table1-1-3-north3-l",
      "#vm-table2-linux-r2-4-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-north3",
      "#vm-table2-2-2-region2-north3",
      "#vm-table-Constrained-2-1-north3",
      "#vm-table1-linux-r2-1-east3",
      "#vm-table1-linux-r2-2-east3",
      "#vm-table1-linux-1-3-east3",
      "#vm-table1-linux-r2-3-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table1-linux-1-6-east3",
      "#vm-table2-linux-1-new-east3",
      "#vm-table1-linux-2-new-east3",
      "#vm-table2-linux-4-new-east3",
      "#vm-table1-linux-3-new-east3",
      "#vm-table1-linux-4-new-east3",
      "#vm-table1-linux-r2-5-east3",
      "#vm-table1-linux-r2-7-east3",
      "#vm-table1-linux-r2-8-east3",
      "#vm-table1-linux-3-1-east3",
      "#vm-table1-linux-3-2-east3",
      "#vm-table1-linux-5-new-east3",
      "#vm-table1-linux-6-new-east3",
      "#vm-table1-linux-7-new-east3",
      "#vm-table1-linux-8-new-east3",
      "#vm-linux-computingoptimization-fsv2-east3",
      "#vm-table1-1-3-east3-l",
      "#vm-table2-linux-r2-4-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-east3",
      "#vm-table2-2-2-region2-east3",
      "#vm-table-Constrained-2-1-east3",
      "#vm-table2-1-5s",
      "#vm-table2-1-7",
      "#vm-table2-3-1",
      "#vm-table2-3-3",
      "#vm-table-linux-memoryprioritization-d15v2",
      "#vm-table-linux-memoryprioritization-ds15v2",
      "#vm-table-linux-computingprioritization-f1-f16",
      "#vm-table2-2-2",
      "#vms-table2-1-7",
      "#vms-table2-3-1",
      "#vms-table2-3-3",
      "#vms-linux-memoryprioritization-ev3-region",
      "#vms-table-linux-memoryprioritization-d15v2",
      "#vms-table-linux-memoryprioritization-ds15v2",
      "#vms-table-linux-computingprioritization-f1-f16",
      "#vms-table2-2-2",
      "#vm-table2-1-1-ml-basic",
      "#vm-table2-1-2-ml-basic",
      "#vm-table2-1-3-ml-basic",
      "#vm-table2-1-4-ml-basic",
      "#vm-table2-1-5-ml-basic",
      "#vm-table2-1-6-ml-basic",
      "#vm-table2-1-7-ml-basic",
      "#vm-table2-1-8-ml-basic",
      "#vm-table2-1-9-ml-basic",
      "#vm-linux-computingoptimization-fsv2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-ml-basic",
      "#vm-table2-2-2-region2-ml-basic",
      "#vm-table2-2-2-ml-basic",
      "#vm-table2-3-1-ml-basic",
      "#vm-linux-memoryprioritization-ev3-region2-ml-basic",
      "#vm-table2-3-2-ml-basic",
      "#vm-table2-3-3-ml-basic",
      "#vm-table2-3-4-ml-basic",
      "#vm-table-linux-memoryprioritization-d15v2-ml-basic",
      "#vm-table2-3-5-ml-basic",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-basic",
      "#vm-table2-3-6-ml-basic",
      "#vm-table2-4-1-ml-basic",
      "#vm-table2-1-1-ml-enterprise",
      "#vm-table2-1-2-ml-enterprise",
      "#vm-table2-1-3-ml-enterprise",
      "#vm-table2-1-4-ml-enterprise",
      "#vm-table2-1-5-ml-enterprise",
      "#vm-table2-1-6-ml-enterprise",
      "#vm-table2-1-7-ml-enterprise",
      "#vm-table2-1-8-ml-enterprise",
      "#vm-table2-1-9-ml-enterprise",
      "#vm-linux-computingoptimization-fsv2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-ml-enterprise",
      "#vm-table2-2-2-region2-ml-enterprise",
      "#vm-table2-2-2-ml-enterprise",
      "#vm-table2-3-1-ml-enterprise",
      "#vm-linux-memoryprioritization-ev3-region2-ml-enterprise",
      "#vm-table2-3-2-ml-enterprise",
      "#vm-table2-3-3-ml-enterprise",
      "#vm-table2-3-4-ml-enterprise",
      "#vm-table-linux-memoryprioritization-d15v2-ml-enterprise",
      "#vm-table2-3-5-ml-enterprise",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-enterprise",
      "#vm-table2-3-6-ml-enterprise",
      "#vm-table2-4-1-ml-enterprise",
      "#vm-table1-linux-r2-1",
      "#vm-table1-linux-r2-2",
      "#vm-table1-linux-r2-3",
      "#vm-table1-linux-r2-4",
      "#vm-table1-linux-r2-5",
      "#vm-table1-linux-r2-6",
      "#vm-table1-linux-r2-7",
      "#vm-table1-linux-r2-8",
      "#vm-table1-linux-r2-9",
      "#vm-table1-linux-r2-10",
      "#vm-table1-linux-r2-11",
      "#vm-table-Constrained-2-3",
      "#vm-table-Constrained-2-1",
      "#vm-table1-d2v51-l-n3",
      "#vm-table1-ev5-l-n3",
      "#vm-table1-ev5-ls-n3",
      "#vm-table1-d2v51-ls"
    ]
  },
  {
    "os": "Linux",
    "region": "north-china",
    "tableIDs": [
      "#vm-table1-Easv4-Linux-N3",
      "#vm-table1-Eav4-Linux-N3",
      "#vm-table1-Dasv5-Linux-N3",
      "#vm-table1-Dadsv5-Linux-N3",
      "#vm-table1-Dav4-Linux-N3",
      "#vm-table1-Dasv4-Linux-N3",
      "#vm-table1-windows-Bsv2-north3-north2-east2",
      "#vm-table1-linux-Bsv2-north3-north2-east2",
      "#vm-table1-windows-Basv2-north3-north2-east2",
      "#vm-table1-linux-Basv2-north3-north2-east2",
      "#vm-table1-windows-B2pts-north3-north2-east2",
      "#vm-table1-linux-B2pts-north3-north2-east2",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-Dldsv5-Windows-N2-E2-N3",
      "#vm-table-Dldsv5-Linux-N2-E2-N3",
      "#vm-table-Dlsv5-Windows-N2-E2-N3",
      "#vm-table-Dlsv5-Linux-N2-E2-N3",
      "#vm-table1-Ebsv5-Windows-N3-N2-E2",
      "#vm-table1-Ebsv5-Linux-N3-N2-E2",
      "#vm-table-NCas_T4_v3-Linux-N3-CPP",
      "#vm-table1-Ebdsv5-Linux-N3-N2-E2",
      "#vm-table1-Eadsv5-Linux-N3",
      "#vm-table1-Easv5-Linux-N2-E2-N3",
      "#vm-table1-Dadsv5-Linux-N2-E2-N3",
      "#vm-table1-Dasv5-Linux-N2-E2-N3",
      "#vm-table1-Easv4-Linux-N3-N2-E2",
      "#vm-table1-Eav4-Linux-N3-N2-E2",
      "#vm-table1-Dasv4-Linux-N2-E2-N3",
      "#vm-table1-Dav4-Linux-N2-E2-N3",
      "#vm-table1-NCsv3-Linux-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3", 
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-ev5-ls-en2",
      "#vm-table1-d2v51-ls-en3",
      "#vm-table1-d2v51-ls",
      "#vm-table1-d2v51-l",
      "#vm-table1-linux-r2-11-north3",
      "#vm-table-mdsv2-l-n3",
      "#vm-table-mdsv2-l-n3-1",
      "#vm-table-mdsv2-l-n3-2",
      "#vm-table1-linux-r2-11-east3",
      "#vm-table1-linux-r2-10-east3",
      "#vm-table2-6-cpp-new-north3",
      "#vm-table1-linux-r2-9-east3",
      "#vm-table1-linux-r2-9-north3",
      "#vm-table1-linux-r2-1-north3",
      "#vm-table1-linux-r2-2-north3",
      "#vm-table1-linux-1-3-north3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-linux-r2-3-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table1-linux-1-6-north3",
      "#vm-table2-linux-1-new-north3",
      "#vm-table1-linux-2-new-north3",
      "#vm-table2-linux-4-new-north3",
      "#vm-table1-linux-3-new-north3",
      "#vm-table1-linux-4-new-north3",
      "#vm-table1-linux-r2-5-north3",
      "#vm-table1-linux-r2-7-north3",
      "#vm-table1-linux-r2-8-north3",
      "#vm-table1-linux-3-1-north3",
      "#vm-table1-linux-3-2-north3",
      "#vm-table1-linux-5-new-north3",
      "#vm-table1-linux-6-new-north3",
      "#vm-table1-linux-7-new-north3",
      "#vm-table1-linux-8-new-north3",
      "#vm-linux-computingoptimization-fsv2-north3",
      "#vm-table1-1-3-north3-l",
      "#vm-table2-linux-r2-4-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-north3",
      "#vm-table2-2-2-region2-north3",
      "#vm-table-Constrained-2-1-north3",
      "#vm-table1-linux-r2-1-east3",
      "#vm-table1-linux-r2-2-east3",
      "#vm-table1-linux-1-3-east3",
      "#vm-table1-linux-r2-3-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table1-linux-1-6-east3",
      "#vm-table2-linux-1-new-east3",
      "#vm-table1-linux-2-new-east3",
      "#vm-table2-linux-4-new-east3",
      "#vm-table1-linux-3-new-east3",
      "#vm-table1-linux-4-new-east3",
      "#vm-table1-linux-r2-5-east3",
      "#vm-table1-linux-r2-7-east3",
      "#vm-table1-linux-r2-8-east3",
      "#vm-table1-linux-3-1-east3",
      "#vm-table1-linux-3-2-east3",
      "#vm-table1-linux-5-new-east3",
      "#vm-table1-linux-6-new-east3",
      "#vm-table1-linux-7-new-east3",
      "#vm-table1-linux-8-new-east3",
      "#vm-linux-computingoptimization-fsv2-east3",
      "#vm-table1-1-3-east3-l",
      "#vm-table2-linux-r2-4-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-east3",
      "#vm-table2-2-2-region2-east3",
      "#vm-table-Constrained-2-1-east3",
      "#vm-table2-1-5s",
      "#vm-table2-1-3",
      "#vm-table2-1-5",
      "#vm-table2-1-6",
      "#vm-table1-r2-4",
      "#vm-table1-linux-r2-4",
      "#vm-linux-memoryprioritization-ev3-region2",
      "#vm-linux-computingoptimization-fsv2",
      "#vm-table-linux-computingprioritization-f1-f16-region2",
      "#vm-table2-2-2-region2",
      "#vm-table2-3-1",
      "#vm-table2-3-2",
      "#vm-table-linux-memoryprioritization-d15v2",
      "#vm-table-linux-memoryprioritization-ds15v2",
      "#vm-table2-3-6",
      "#vm-table2-4-1",
      "#vms-table2-1-3",
      "#vms-table2-1-5",
      "#vms-table2-1-6",
      "#vms-linux-memoryprioritization-ev3-region2",
      "#vms-linux-computingoptimization-fsv2",
      "#vms-table-linux-computingprioritization-f1-f16-region2",
      "#vms-table2-2-2-region2",
      "#vms-table2-3-1",
      "#vms-table2-3-2",
      "#vms-table-linux-memoryprioritization-d15v2",
      "#vms-table-linux-memoryprioritization-ds15v2",
      "#vms-table2-3-6",
      "#vms-table2-4-1",
      "#vm-table2-1-1-ml",
      "#vm-table2-1-2-ml",
      "#vm-table2-1-3-ml",
      "#vm-table2-1-4-ml",
      "#vm-table2-1-5-ml",
      "#vm-table2-1-6-ml",
      "#vm-table2-1-7-ml",
      "#vm-table2-1-8-ml",
      "#vm-table2-1-9-ml",
      "#vm-linux-computingoptimization-fsv2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-ml",
      "#vm-table2-2-2-region2-ml",
      "#vm-table2-2-2-ml",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml",
      "#vm-table2-3-2-ml",
      "#vm-table2-3-3-ml",
      "#vm-table2-3-4-ml",
      "#vm-table-linux-memoryprioritization-d15v2-ml",
      "#vm-table2-3-5-ml",
      "#vm-table-linux-memoryprioritization-ds15v2-ml",
      "#vm-table2-3-6-ml",
      "#vm-table2-4-1-ml",
      "#vm-table2-1-1-ml-basic",
      "#vm-table2-1-2-ml-basic",
      "#vm-table2-1-3-ml-basic",
      "#vm-table2-1-4-ml-basic",
      "#vm-table2-1-5-ml-basic",
      "#vm-table2-1-6-ml-basic",
      "#vm-table2-1-7-ml-basic",
      "#vm-table2-1-8-ml-basic",
      "#vm-table2-1-9-ml-basic",
      "#vm-linux-computingoptimization-fsv2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-ml-basic",
      "#vm-table2-2-2-region2-ml-basic",
      "#vm-table2-2-2-ml-basic",
      "#vm-table2-3-1-ml-basic",
      "#vm-linux-memoryprioritization-ev3-region2-ml-basic",
      "#vm-table2-3-2-ml-basic",
      "#vm-table2-3-3-ml-basic",
      "#vm-table2-3-4-ml-basic",
      "#vm-table-linux-memoryprioritization-d15v2-ml-basic",
      "#vm-table2-3-5-ml-basic",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-basic",
      "#vm-table2-3-6-ml-basic",
      "#vm-table2-4-1-ml-basic",
      "#vm-table2-1-1-ml-enterprise",
      "#vm-table2-1-2-ml-enterprise",
      "#vm-table2-1-3-ml-enterprise",
      "#vm-table2-1-4-ml-enterprise",
      "#vm-table2-1-5-ml-enterprise",
      "#vm-table2-1-6-ml-enterprise",
      "#vm-table2-1-7-ml-enterprise",
      "#vm-table2-1-8-ml-enterprise",
      "#vm-table2-1-9-ml-enterprise",
      "#vm-linux-computingoptimization-fsv2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-ml-enterprise",
      "#vm-table2-2-2-region2-ml-enterprise",
      "#vm-table2-2-2-ml-enterprise",
      "#vm-table2-3-1-ml-enterprise",
      "#vm-linux-memoryprioritization-ev3-region2-ml-enterprise",
      "#vm-table2-3-2-ml-enterprise",
      "#vm-table2-3-3-ml-enterprise",
      "#vm-table2-3-4-ml-enterprise",
      "#vm-table-linux-memoryprioritization-d15v2-ml-enterprise",
      "#vm-table2-3-5-ml-enterprise",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-enterprise",
      "#vm-table2-3-6-ml-enterprise",
      "#vm-table2-4-1-ml-enterprise",
      "#vm-table2-1-new",
      "#vm-table2-2-new",
      "#vm-table2-3-new",
      "#vm-table2-4-new",
      "#vm-table2-5-new",
      "#vm-table2-6-new",
      "#vm-table2-7-new",
      "#vm-table2-8-new",
      "#vm-table2-1-cpp-new",
      "#vm-table2-2-cpp-new",
      "#vm-table2-3-cpp-new",
      "#vm-table2-4-cpp-new",
      "#vm-table2-5-cpp-new",
      "#vm-table2-6-cpp-new",
      "#vm-table2-7-cpp-new",
      "#vm-table2-8-cpp-new",
      "#vm-table2-9-cpp-new",
      "#vm-table-Constrained-2-1",
      "#vm-table-Constrained-2-2",
      "#vm-table1-d2v51-l-n3",
      "#vm-table1-ev5-l-n3",
      "#vm-table1-ev5-ls-n3",
      "#vm-table1-ev5-l",
      "#vm-table1-ev5-ls"
    ]
  },
  {
    "os": "Linux",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table1-Easv4-Linux-N3",
      "#vm-table1-Eav4-Linux-N3",
      "#vm-table1-Dasv5-Linux-N3",
      "#vm-table1-Dadsv5-Linux-N3",
      "#vm-table1-Dav4-Linux-N3",
      "#vm-table1-Dasv4-Linux-N3",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-NCas_T4_v3-Linux-N3-CPP",
      "#vm-table1-Eadsv5-Linux-N3",
      "#vm-table1-Easv5-Linux-N2-E2-N3",
      "#vm-table1-NCsv3-Linux-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3", 
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-linux-r2-11-north3",
      "#vm-table1-linux-r2-11-east3",
      "#vm-table1-linux-r2-10-east3",
      "#vm-table-mdsv2-l-n3",
      "#vm-table-mdsv2-l-n3-1",
      "#vm-table-mdsv2-l-n3-2",
      "#vm-table2-6-cpp-new-north3",
      "#vm-table1-linux-r2-9-east3",
      "#vm-table1-linux-r2-9-north3",
      "#vm-table1-linux-r2-1-north3",
      "#vm-table1-linux-r2-2-north3",
      "#vm-table1-linux-1-3-north3",
      "#vm-table1-linux-r2-3-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table1-linux-1-6-north3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table2-linux-1-new-north3",
      "#vm-table1-linux-2-new-north3",
      "#vm-table2-linux-4-new-north3",
      "#vm-table1-linux-3-new-north3",
      "#vm-table1-linux-4-new-north3",
      "#vm-table1-linux-r2-5-north3",
      "#vm-table1-linux-r2-7-north3",
      "#vm-table1-linux-r2-8-north3",
      "#vm-table1-linux-3-1-north3",
      "#vm-table1-linux-3-2-north3",
      "#vm-table1-linux-5-new-north3",
      "#vm-table1-linux-6-new-north3",
      "#vm-table1-linux-7-new-north3",
      "#vm-table1-linux-8-new-north3",
      "#vm-linux-computingoptimization-fsv2-north3",
      "#vm-table1-1-3-north3-l",
      "#vm-table2-linux-r2-4-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-north3",
      "#vm-table2-2-2-region2-north3",
      "#vm-table-Constrained-2-1-north3",
      "#vm-table1-linux-r2-1-east3",
      "#vm-table1-linux-r2-2-east3",
      "#vm-table1-linux-1-3-east3",
      "#vm-table1-linux-r2-3-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table1-linux-1-6-east3",
      "#vm-table2-linux-1-new-east3",
      "#vm-table1-linux-2-new-east3",
      "#vm-table2-linux-4-new-east3",
      "#vm-table1-linux-3-new-east3",
      "#vm-table1-linux-4-new-east3",
      "#vm-table1-linux-r2-5-east3",
      "#vm-table1-linux-r2-7-east3",
      "#vm-table1-linux-r2-8-east3",
      "#vm-table1-linux-3-1-east3",
      "#vm-table1-linux-3-2-east3",
      "#vm-table1-linux-5-new-east3",
      "#vm-table1-linux-6-new-east3",
      "#vm-table1-linux-7-new-east3",
      "#vm-table1-linux-8-new-east3",
      "#vm-linux-computingoptimization-fsv2-east3",
      "#vm-table1-1-3-east3-l",
      "#vm-table2-linux-r2-4-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-east3",
      "#vm-table2-2-2-region2-east3",
      "#vm-table-Constrained-2-1-east3",
      "#vm-table2-1-5s",
      "#vm-table2-1-7",
      "#vm-table2-3-3",
      "#vm-table2-3-1",
      "#vm-table-linux-memoryprioritization-d15v2",
      "#vm-table-linux-memoryprioritization-ds15v2",
      "#vm-table-linux-computingprioritization-f1-f16",
      "#vm-table2-2-2",
      "#vms-table2-1-7",
      "#vms-table2-3-3",
      "#vms-table2-3-1",
      "#vms-table-linux-memoryprioritization-d15v2",
      "#vms-table-linux-memoryprioritization-ds15v2",
      "#vms-table-linux-computingprioritization-f1-f16",
      "#vms-table2-2-2",
      "#vm-table2-1-7-ml",
      "#vm-table-linux-computingprioritization-f1-f16-ml",
      "#vm-table2-2-2-ml",
      "#vm-table2-3-1-ml",
      "#vm-table2-3-3-ml",
      "#vm-table-linux-memoryprioritization-d15v2-ml",
      "#vm-table-linux-memoryprioritization-ds15v2-ml",
      "#vm-table2-1-7-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-ml-basic",
      "#vm-table2-2-2-ml-basic",
      "#vm-table2-3-1-ml-basic",
      "#vm-table2-3-3-ml-basic",
      "#vm-table-linux-memoryprioritization-d15v2-ml-basic",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-basic",
      "#vm-table2-1-7-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-ml-enterprise",
      "#vm-table2-2-2-ml-enterprise",
      "#vm-table2-3-1-ml-enterprise",
      "#vm-table2-3-3-ml-enterprise",
      "#vm-table-linux-memoryprioritization-d15v2-ml-enterprise",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-enterprise",
      "#vm-table1-linux-r2-1",
      "#vm-table1-linux-r2-2",
      "#vm-table1-linux-r2-3",
      "#vm-table1-linux-r2-4",
      "#vm-table1-linux-r2-5",
      "#vm-table1-linux-r2-6",
      "#vm-table1-linux-r2-7",
      "#vm-table1-linux-r2-8",
      "#vm-table1-linux-r2-9",
      "#vm-table1-linux-r2-10",
      "#vm-table1-linux-r2-11",
      "#vm-table-Constrained-2-2",
      "#vm-table-Constrained-2-3",
      "#vm-table1-d2v51-l-n3",
      "#vm-table1-ev5-l-n3",
      "#vm-table1-ev5-ls-n3",
      "#vm-table1-d2v51-ls"
    ]
  },
  {
    "os": "Linux",
    "region": "east-china",
    "tableIDs": [
      "#vm-table1-Easv4-Linux-N3",
      "#vm-table1-Eav4-Linux-N3",
      "#vm-table1-Dasv5-Linux-N3",
      "#vm-table1-Dadsv5-Linux-N3",
      "#vm-table1-Dav4-Linux-N3",
      "#vm-table1-Dasv4-Linux-N3", 
      "#vm-table1-windows-Bsv2-north3-north2-east2",
      "#vm-table1-linux-Bsv2-north3-north2-east2",
      "#vm-table1-windows-Basv2-north3-north2-east2",
      "#vm-table1-linux-Basv2-north3-north2-east2",
      "#vm-table1-windows-B2pts-north3-north2-east2",
      "#vm-table1-linux-B2pts-north3-north2-east2",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-Dldsv5-Windows-N2-E2-N3",
      "#vm-table-Dldsv5-Linux-N2-E2-N3", 
      "#vm-table-Dlsv5-Windows-N2-E2-N3",
      "#vm-table-Dlsv5-Linux-N2-E2-N3",
      "#vm-table1-Ebsv5-Windows-N3-N2-E2",
      "#vm-table1-Ebsv5-Linux-N3-N2-E2",
      "#vm-table-NCas_T4_v3-Linux-N3-CPP",  
      "#vm-table1-Ebdsv5-Linux-N3-N2-E2",  
    "#vm-table1-Eadsv5-Linux-N3",
      "#vm-table1-Easv5-Linux-N2-E2-N3",
      "#vm-table1-Dadsv5-Linux-N2-E2-N3",  
    "#vm-table1-Dasv5-Linux-N2-E2-N3",  
    "#vm-table1-Easv4-Linux-N3-N2-E2",  
    "#vm-table1-Eav4-Linux-N3-N2-E2",  
    "#vm-table1-Dasv4-Linux-N2-E2-N3",
      "#vm-table1-Dav4-Linux-N2-E2-N3",
      "#vm-table1-NCsv3-Linux-N3",
      "#vm-table1-NVv4-windows-N3",
      "#vm-table1-NVv4-Linux-N3", 
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-ev5-ls-en2",
      "#vm-table1-d2v51-ls-en3",
      "#vm-table1-d2v51-ls",
      "#vm-table1-d2v51-l",
      "#vm-table1-ev5-l",
      "#vm-table1-linux-r2-11-north3",
      "#vm-table1-linux-r2-11-east3",
      "#vm-table1-linux-r2-10-east3",
      "#vm-table-mdsv2-l-n3",
      "#vm-table-mdsv2-l-n3-1",
      "#vm-table-mdsv2-l-n3-2",
      "#vm-table2-6-cpp-new-north3",
      "#vm-table1-linux-r2-9-east3",
      "#vm-table1-linux-r2-9-north3",
      "#vm-table1-linux-r2-1-north3",
      "#vm-table1-linux-r2-2-north3",
      "#vm-table1-linux-1-3-north3",
      "#vm-table1-linux-r2-3-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table1-linux-1-6-north3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table2-linux-1-new-north3",
      "#vm-table1-linux-2-new-north3",
      "#vm-table2-linux-4-new-north3",
      "#vm-table1-linux-3-new-north3",
      "#vm-table1-linux-4-new-north3",
      "#vm-table1-linux-r2-5-north3",
      "#vm-table1-linux-r2-7-north3",
      "#vm-table1-linux-r2-8-north3",
      "#vm-table1-linux-3-1-north3",
      "#vm-table1-linux-3-2-north3",
      "#vm-table1-linux-5-new-north3",
      "#vm-table1-linux-6-new-north3",
      "#vm-table1-linux-7-new-north3",
      "#vm-table1-linux-8-new-north3",
      "#vm-linux-computingoptimization-fsv2-north3",
      "#vm-table1-1-3-north3-l",
      "#vm-table2-linux-r2-4-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-north3",
      "#vm-table2-2-2-region2-north3",
      "#vm-table-Constrained-2-1-north3",
      "#vm-table1-linux-r2-1-east3",
      "#vm-table1-linux-r2-2-east3",
      "#vm-table1-linux-1-3-east3",
      "#vm-table1-linux-r2-3-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table1-linux-1-6-east3",
      "#vm-table2-linux-1-new-east3",
      "#vm-table1-linux-2-new-east3",
      "#vm-table2-linux-4-new-east3",
      "#vm-table1-linux-3-new-east3",
      "#vm-table1-linux-4-new-east3",
      "#vm-table1-linux-r2-5-east3",
      "#vm-table1-linux-r2-7-east3",
      "#vm-table1-linux-r2-8-east3",
      "#vm-table1-linux-3-1-east3",
      "#vm-table1-linux-3-2-east3",
      "#vm-table1-linux-5-new-east3",
      "#vm-table1-linux-6-new-east3",
      "#vm-table1-linux-7-new-east3",
      "#vm-table1-linux-8-new-east3",
      "#vm-linux-computingoptimization-fsv2-east3",
      "#vm-table1-1-3-east3-l",
      "#vm-table2-linux-r2-4-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-east3",
      "#vm-table2-2-2-region2-east3",
      "#vm-table-Constrained-2-1-east3",
      "#vm-table2-1-5",
      "#vm-table2-1-3",
      "#vm-table2-1-6",
      "#vm-linux-computingoptimization-fsv2",
      "#vm-table-linux-computingprioritization-f1-f16-region2",
      "#vm-table2-2-2-region2",
      "#vm-linux-memoryprioritization-ev3-region2",
      "#vm-table2-3-2",
      "#vm-table-linux-memoryprioritization-d15v2",
      "#vm-table-linux-memoryprioritization-ds15v2",
      "#vm-table2-3-6",
      "#vm-table2-4-1",
      "#vms-table2-1-3",
      "#vms-table2-1-6",
      "#vms-linux-computingoptimization-fsv2",
      "#vms-table-linux-computingprioritization-f1-f16-region2",
      "#vms-table2-2-2-region2",
      "#vms-linux-memoryprioritization-ev3-region2",
      "#vms-table2-3-2",
      "#vms-table-linux-memoryprioritization-d15v2",
      "#vms-table-linux-memoryprioritization-ds15v2",
      "#vms-table2-3-6",
      "#vms-table2-4-1",
      "#vm-table2-1-1-ml",
      "#vm-table2-1-2-ml",
      "#vm-table2-1-3-ml",
      "#vm-table2-1-4-ml",
      "#vm-table2-1-5-ml",
      "#vm-table2-1-6-ml",
      "#vm-table2-1-7-ml",
      "#vm-table2-1-8-ml",
      "#vm-table2-1-9-ml",
      "#vm-linux-computingoptimization-fsv2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-ml",
      "#vm-table2-2-2-region2-ml",
      "#vm-table2-2-2-ml",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml",
      "#vm-table2-3-2-ml",
      "#vm-table2-3-3-ml",
      "#vm-table2-3-4-ml",
      "#vm-table-linux-memoryprioritization-d15v2-ml",
      "#vm-table2-3-5-ml",
      "#vm-table-linux-memoryprioritization-ds15v2-ml",
      "#vm-table2-3-6-ml",
      "#vm-table2-4-1-ml",
      "#vm-table2-1-1-ml-basic",
      "#vm-table2-1-2-ml-basic",
      "#vm-table2-1-3-ml-basic",
      "#vm-table2-1-4-ml-basic",
      "#vm-table2-1-5-ml-basic",
      "#vm-table2-1-6-ml-basic",
      "#vm-table2-1-7-ml-basic",
      "#vm-table2-1-8-ml-basic",
      "#vm-table2-1-9-ml-basic",
      "#vm-linux-computingoptimization-fsv2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-ml-basic",
      "#vm-table2-2-2-region2-ml-basic",
      "#vm-table2-2-2-ml-basic",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml-basic",
      "#vm-table2-3-2-ml-basic",
      "#vm-table2-3-3-ml-basic",
      "#vm-table2-3-4-ml-basic",
      "#vm-table-linux-memoryprioritization-d15v2-ml-basic",
      "#vm-table2-3-5-ml-basic",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-basic",
      "#vm-table2-3-6-ml-basic",
      "#vm-table2-4-1-ml-basic",
      "#vm-table2-1-1-ml-enterprise",
      "#vm-table2-1-2-ml-enterprise",
      "#vm-table2-1-3-ml-enterprise",
      "#vm-table2-1-4-ml-enterprise",
      "#vm-table2-1-5-ml-enterprise",
      "#vm-table2-1-6-ml-enterprise",
      "#vm-table2-1-7-ml-enterprise",
      "#vm-table2-1-8-ml-enterprise",
      "#vm-table2-1-9-ml-enterprise",
      "#vm-linux-computingoptimization-fsv2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-ml-enterprise",
      "#vm-table2-2-2-region2-ml-enterprise",
      "#vm-table2-2-2-ml-enterprise",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml-enterprise",
      "#vm-table2-3-2-ml-enterprise",
      "#vm-table2-3-3-ml-enterprise",
      "#vm-table2-3-4-ml-enterprise",
      "#vm-table-linux-memoryprioritization-d15v2-ml-enterprise",
      "#vm-table2-3-5-ml-enterprise",
      "#vm-table-linux-memoryprioritization-ds15v2-ml-enterprise",
      "#vm-table2-3-6-ml-enterprise",
      "#vm-table2-4-1-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-ml-enterprise",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-basic",
      "#vm-table-linux-computingprioritization-f1-f16-ml-basic",
      "#vm-table2-3-1-ml-basic",
      "#vm-table2-3-1-ml-enterprise",
      "#vm-table2-1-new",
      "#vm-table2-2-new",
      "#vm-table2-3-new",
      "#vm-table2-4-new",
      "#vm-table2-5-new",
      "#vm-table2-6-new",
      "#vm-table2-7-new",
      "#vm-table2-8-new",
      "#vm-table2-1-cpp-new",
      "#vm-table2-2-cpp-new",
      "#vm-table2-3-cpp-new",
      "#vm-table2-4-cpp-new",
      "#vm-table2-5-cpp-new",
      "#vm-table2-6-cpp-new",
      "#vm-table2-7-cpp-new",
      "#vm-table2-8-cpp-new",
      "#vm-table2-9-cpp-new",
      "#vm-table-Constrained-2-1",
      "#vm-table-Constrained-2-2",
      "#vm-table1-d2v51-l-n3",
      "#vm-table1-ev5-l-n3",
      "#vm-table1-ev5-ls-n3"
    ]
  },
  {
    "os": "Linux",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table1-windows-Bsv2-north3-north2-east2",
      "#vm-table1-linux-Bsv2-north3-north2-east2",
      "#vm-table1-windows-Basv2-north3-north2-east2",
      "#vm-table1-linux-Basv2-north3-north2-east2",
      "#vm-table1-windows-B2pts-north3-north2-east2",
      "#vm-table1-linux-B2pts-north3-north2-east2",
      "#vm-table1-NVads-Windows-N3",
      "#vm-table1-NVads-Linux-N3",
      "#vm-table1-HBv3-Windows-N3",
      "#vm-table1-HBv3-Constrained-Windows-N3",
      "#vm-table1-HBv3-Linux-N3",
      "#vm-table1-HBv3-Constrained-Linux-N3",
      "#vm-table-Dldsv5-Windows-N2-E2-N3",
      "#vm-table-Dldsv5-Linux-N2-E2-N3",
      "#vm-table-Dlsv5-Windows-N2-E2-N3",
      "#vm-table-Dlsv5-Linux-N2-E2-N3",
      "#vm-table1-Ebsv5-Windows-N3-N2-E2",
      "#vm-table1-Ebsv5-Linux-N3-N2-E2",
      "#vm-table-NCas_T4_v3-Linux-N3-CPP",
      "#vm-table1-L80s-v3-windows",
      "#vm-table2-L80s-v3-windows",
      "#vm-table1-L80s-v3-linux",
      "#vm-table2-L80s-v3-linux",
      "#vm-table-Lasv3-Windows-N3",
      "#vm-table-Lasv3-Linux-N3",
      "#vm-table1-ev5-ls-en2",
      "#vm-table1-d2v51-ls-en3",
      "#vm-table1-d2v51-l",
      "#vm-table1-linux-r2-11-north3",
      "#vm-table-mdsv2-l-n3",
      "#vm-table-mdsv2-l-n3-1",
      "#vm-table-mdsv2-l-n3-2",
      "#vm-table2-6-cpp-new-north3",
      "#vm-table1-linux-r2-9-north3",
      "#vm-table1-linux-r2-1-north3",
      "#vm-table1-linux-r2-2-north3",
      "#vm-table1-linux-1-3-north3",
      "#vm-table1-linux-r2-3-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table2-5-cpp-new-north3",
      "#vm-table1-linux-1-6-north3",
      "#vm-table2-linux-1-new-north3",
      "#vm-table1-linux-2-new-north3",
      "#vm-table2-linux-4-new-north3",
      "#vm-table1-linux-r2-5-north3",
      "#vm-table1-linux-r2-7-north3",
      "#vm-table1-linux-r2-8-north3",
      "#vm-table1-linux-3-1-north3",
      "#vm-table1-linux-3-2-north3",
      "#vm-table1-linux-5-new-north3",
      "#vm-table1-linux-6-new-north3",
      "#vm-table1-linux-7-new-north3",
      "#vm-table1-linux-8-new-north3",
      "#vm-linux-computingoptimization-fsv2-north3",
      "#vm-table1-1-3-north3-l",
      "#vm-table2-linux-r2-4-north3",
      "#vm-table1-linux-r2-4-north3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-north3",
      "#vm-table2-2-2-region2-north3",
      "#vm-table-Constrained-2-1-north3",
      "#vm-table2-3-2",
      "#vm-table2-1-5",
      "#vm-table1-linux-r2-11",
      "#vm-table1-linux-r2-1",
      "#vm-table1-linux-r2-2",
      "#vm-table1-linux-1-3",
      "#vm-table1-linux-r2-3",
      "#vm-table1-linux-r2-4",
      "#vm-table1-linux-1-6",
      "#vm-table1-linux-r2-5",
      "#vm-table1-linux-r2-7",
      "#vm-table1-linux-r2-8",
      "#vm-table1-linux-3-1",
      "#vm-table1-linux-r2-6",
      "#vm-table1-linux-3-2",
      "#vm-table1-linux-3-3",
      "#vm-table1-linux-r2-9",
      "#vm-table1-linux-r2-10",
      "#vm-table1-linux-3-6",
      "#vm-table1-linux-4-1",
      "#vm-table2-1-cpp-new",
      "#vm-table2-2-cpp-new",
      "#vm-table2-r2-3-cpp",
      "#vm-table2-4-cpp-new",
      "#vm-table2-1-new",
      "#vm-table2-2-new",
      "#vm-table2-3-new",
      "#vm-table2-4-new",
      "#vm-table2-5-cpp-new",
      "#vm-table2-7-cpp-new",
      "#vm-table2-8-cpp-new",
      "#vm-table2-6-cpp-new",
      "#vm-table2-5-new",
      "#vm-table2-6-new",
      "#vm-table2-7-new",
      "#vm-table2-8-new",
      "#vm-table2-9-cpp-new",
      "#vm-table2-11-cpp-new",
      "#vm-table2-1-3",
      "#vm-table2-3-cpp-new",
      "#vm-table2-linux-r2-4",
      "#vm-table2-1-7",
      "#vm-linux-computingoptimization-fsv2",
      "#vm-table-linux-computingprioritization-f1-f16-region2",
      "#vm-table2-2-2-region2",
      "#vm-linux-memoryprioritization-ev3-region2",
      "#vm-table2-3-3",
      "#vm-table2-3-6",
      "#vm-table-Constrained-2-1",
      "#vm-table-Constrained-2-3",
      "#vm-table-Constrained-2-2",
      "#vm-table2-4-1",
      "#vm-table1-linux-3-new-east3",
      "#vm-table1-linux-4-new-east3",
      "#vm-table1-Dav4-Linux-N2-E2-N3",
      "#vm-table1-Dadsv5-Linux-N2-E2-N3",
      "#vm-table1-Dasv5-Linux-N2-E2-N3",
      "#vm-table1-ddsv5-Win-E3",
      "#vm-table1-ev5-l",
      "#vm-table1-ev5-ls"
    ]
  },
  {
    "os": "Linux",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table1-Easv4-Linux-N3-N2-E2",
      "#vm-table1-Eav4-Linux-N3-N2-E2",
      "#vm-table1-Dasv5-Linux-N2-E2-N3",
      "#vm-table1-Dadsv5-Linux-N2-E2-N3",
      "#vm-table1-Dav4-Linux-N2-E2-N3",
      "#vm-table1-Dasv4-Linux-N2-E2-N3",     
      "#vm-table2-1-6",
      "#vm-table1-linux-r2-11-east3",
      "#vm-table1-linux-r2-10-east3",
      "#vm-table1-linux-r2-1-east3",
      "#vm-table1-linux-r2-9-east3",
      "#vm-table1-linux-r2-2-east3",
      "#vm-table1-linux-1-3-east3",
      "#vm-table1-linux-r2-3-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table1-linux-1-6-east3",
      "#vm-table2-linux-1-new-east3",
      "#vm-table1-linux-2-new-east3",
      "#vm-table2-linux-4-new-east3",
      "#vm-table1-linux-3-new-east3",
      "#vm-table2-5-cpp-new-east3",
      "#vm-table1-linux-4-new-east3",
      "#vm-table1-linux-r2-5-east3",
      "#vm-table1-linux-r2-7-east3",
      "#vm-table1-linux-r2-8-east3",
      "#vm-table1-linux-3-1-east3",
      "#vm-table1-linux-3-2-east3",
      "#vm-table1-linux-5-new-east3",
      "#vm-table1-linux-6-new-east3",
      "#vm-table1-linux-7-new-east3",
      "#vm-table1-linux-8-new-east3",
      "#vm-linux-computingoptimization-fsv2-east3",
      "#vm-table1-1-3-east3-l",
      "#vm-table2-linux-r2-4-east3",
      "#vm-table1-linux-r2-4-east3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-east3",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml-basic",
      "#vm-table2-2-2-region2-east3",
      "#vm-table2-2-2-ml-basic",
      "#vm-table2-3-1-ml-basic",
      "#vm-table-Constrained-2-1-east3",
      "#vm-table2-3-2",
      "#vm-table2-1-5",
      "#vm-table1-linux-r2-11",
      "#vm-table1-linux-r2-1",
      "#vm-table1-linux-r2-2",
      "#vm-table1-linux-1-3",
      "#vm-table1-linux-r2-3",
      "#vm-table1-linux-r2-4",
      "#vm-table1-linux-1-6",
      "#vm-table1-linux-r2-5",
      "#vm-table1-linux-r2-7",
      "#vm-table1-linux-r2-8",
      "#vm-table1-linux-3-1",
      "#vm-table1-linux-r2-6",
      "#vm-table1-linux-3-2",
      "#vm-table1-linux-3-3",
      "#vm-table1-linux-r2-9",
      "#vm-table1-linux-r2-10",
      "#vm-table1-linux-3-6",
      "#vm-table1-linux-4-1",
      "#vm-table2-1-cpp-new",
      "#vm-table2-2-cpp-new",
      "#vm-table2-r2-3-cpp",
      "#vm-table2-4-cpp-new",
      "#vm-table2-1-new",
      "#vm-table2-2-new",
      "#vm-table2-3-new",
      "#vm-table2-4-new",
      "#vm-table2-5-cpp-new",
      "#vm-table2-7-cpp-new",
      "#vm-table2-8-cpp-new",
      "#vm-table2-6-cpp-new",
      "#vm-table2-5-new",
      "#vm-table2-6-new",
      "#vm-table2-7-new",
      "#vm-table2-8-new",
      "#vm-table2-9-cpp-new",
      "#vm-table2-11-cpp-new",
      "#vm-table2-1-3",
      "#vm-table2-3-cpp-new",
      "#vm-table2-linux-r2-4",
      "#vm-table2-1-7",
      "#vm-linux-computingoptimization-fsv2",
      "#vm-table-linux-computingprioritization-f1-f16-region2",
      "#vm-table2-2-2-region2",
      "#vm-linux-memoryprioritization-ev3-region2",
      "#vm-table2-3-3",
      "#vm-table-Constrained-2-1",
      "#vm-table-Constrained-2-3",
      "#vm-table-Constrained-2-2",
      "#vm-table2-4-1",
      "#vm-table1-d2v51-l-n3",
      "#vm-table1-ev5-l",
      "#vm-table1-ev5-ls",
      "#vm-table1-d2v51-ls"
    ]
  },
  {
    "os": "SQL Server for Windows",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table3-1-7",
      "#vm-table3-3-1",
      "#vm-table3-3-3",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-1-7",
      "#vms-table3-3-1",
      "#vms-table3-3-3",
      "#vms-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vms-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table-Constrained-3-2",
      "#vm-table-Constrained-3-3"
    ]
  },
  {
    "os": "SQL Server for Windows",
    "region": "north-china",
    "tableIDs": [
      "#vm-table3-1-3",
      "#vm-table3-1-5",
      "#vm-table3-1-6",
      "#vm-table3-3-1",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-4-1",
      "#vms-table3-1-3",
      "#vms-table3-1-5",
      "#vms-table3-1-6",
      "#vms-table3-3-1",
      "#vms-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vms-table3-3-2",
      "#vms-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vms-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-4-1",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table3-1-new",
      "#vm-table3-2-new",
      "#vm-table3-3-new",
      "#vm-table3-4-new",
      "#vm-table3-5-new",
      "#vm-table3-6-new",
      "#vm-table3-7-new",
      "#vm-table3-8-new",
      "#vm-table-Constrained-3-1",
      "#vm-table-Constrained-3-2"
    ]
  },
  {
    "os": "SQL Server for Windows",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table3-1-7",
      "#vm-table3-3-1",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-1-7",
      "#vms-table3-3-1",
      "#vms-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table-Constrained-3-2",
      "#vm-table-Constrained-3-3"
    ]
  },
  {
    "os": "SQL Server for Windows",
    "region": "east-china",
    "tableIDs": [
      "#vm-table3-1-3",
      "#vm-table3-1-6",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-4-1",
      "#vms-table3-1-3",
      "#vms-table3-1-6",
      "#vms-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vms-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vms-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-4-1",
      "#vm-table2-1-1-ml",
      "#vm-table2-1-2-ml",
      "#vm-table2-1-3-ml",
      "#vm-table2-1-4-ml",
      "#vm-table2-1-5-ml",
      "#vm-table2-1-6-ml",
      "#vm-table2-1-7-ml",
      "#vm-table2-1-8-ml",
      "#vm-table2-1-9-ml",
      "#vm-linux-computingoptimization-fsv2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-ml",
      "#vm-table2-2-2-region2-ml",
      "#vm-table2-2-2-ml",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml",
      "#vm-table2-3-2-ml",
      "#vm-table2-3-3-ml",
      "#vm-table2-3-4-ml",
      "#vm-table-linux-memoryprioritization-d15v2-ml",
      "#vm-table2-3-5-ml",
      "#vm-table-linux-memoryprioritization-ds15v2-ml",
      "#vm-table2-3-6-ml",
      "#vm-table2-4-1-ml",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table3-1-new",
      "#vm-table3-2-new",
      "#vm-table3-3-new",
      "#vm-table3-4-new",
      "#vm-table3-5-new",
      "#vm-table3-6-new",
      "#vm-table3-7-new",
      "#vm-table3-8-new",
      "#vm-table-Constrained-3-1",
      "#vm-table-Constrained-3-3"
    ]
  },
  {
    "os": "SQL Server for Windows",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table3-1-3",
      "#vm-table3-1-6",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-4-1",
      "#vms-table3-1-3",
      "#vms-table3-1-6",
      "#vms-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vms-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vms-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-4-1",
      "#vm-table2-1-1-ml",
      "#vm-table2-1-2-ml",
      "#vm-table2-1-3-ml",
      "#vm-table2-1-4-ml",
      "#vm-table2-1-5-ml",
      "#vm-table2-1-6-ml",
      "#vm-table2-1-7-ml",
      "#vm-table2-1-8-ml",
      "#vm-table2-1-9-ml",
      "#vm-linux-computingoptimization-fsv2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-ml",
      "#vm-table2-2-2-region2-ml",
      "#vm-table2-2-2-ml",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml",
      "#vm-table2-3-2-ml",
      "#vm-table2-3-3-ml",
      "#vm-table2-3-4-ml",
      "#vm-table-linux-memoryprioritization-d15v2-ml",
      "#vm-table2-3-5-ml",
      "#vm-table-linux-memoryprioritization-ds15v2-ml",
      "#vm-table2-3-6-ml",
      "#vm-table2-4-1-ml",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table3-1-new",
      "#vm-table3-2-new",
      "#vm-table3-3-new",
      "#vm-table3-4-new",
      "#vm-table3-5-new",
      "#vm-table3-6-new",
      "#vm-table3-7-new",
      "#vm-table3-8-new",
      "#vm-table-Constrained-3-1",
      "#vm-table-Constrained-3-3",
      "#vm-table3-1-7",
      "#vm-table3-3-1",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-1-7",
      "#vms-table3-3-1",
      "#vms-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table-Constrained-3-2",
      "#vm-table-Constrained-3-3",
      "#vm-table3-1-1",
      "#vm-table3-1-2",
      "#vm-table3-1-4",
      "#vm-table3-1-5",
      "#vm-table3-1-8",
      "#vm-table3-1-9",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2",
      "#vm-table-sqlwindows-computingprioritization-f1-f16",
      "#vm-table3-2-1",
      "#vm-table3-3-3",
      "#vm-table3-3-4",
      "#vm-table3-3-5",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries"
    ]
  },
  {
    "os": "SQL Server for Windows",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table3-1-3",
      "#vm-table3-1-6",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-4-1",
      "#vms-table3-1-3",
      "#vms-table3-1-6",
      "#vms-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-3-2",
      "#vms-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vms-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-4-1",
      "#vm-table2-1-1-ml",
      "#vm-table2-1-2-ml",
      "#vm-table2-1-3-ml",
      "#vm-table2-1-4-ml",
      "#vm-table2-1-5-ml",
      "#vm-table2-1-6-ml",
      "#vm-table2-1-7-ml",
      "#vm-table2-1-8-ml",
      "#vm-table2-1-9-ml",
      "#vm-linux-computingoptimization-fsv2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-linux-computingprioritization-f1-f16-ml",
      "#vm-table2-2-2-region2-ml",
      "#vm-table2-2-2-ml",
      "#vm-table2-3-1-ml",
      "#vm-linux-memoryprioritization-ev3-region2-ml",
      "#vm-table2-3-2-ml",
      "#vm-table2-3-3-ml",
      "#vm-table2-3-4-ml",
      "#vm-table-linux-memoryprioritization-d15v2-ml",
      "#vm-table2-3-5-ml",
      "#vm-table-linux-memoryprioritization-ds15v2-ml",
      "#vm-table2-3-6-ml",
      "#vm-table2-4-1-ml",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table3-1-new",
      "#vm-table3-2-new",
      "#vm-table3-3-new",
      "#vm-table3-4-new",
      "#vm-table3-5-new",
      "#vm-table3-6-new",
      "#vm-table3-7-new",
      "#vm-table3-8-new",
      "#vm-table-Constrained-3-1",
      "#vm-table-Constrained-3-3",
      "#vm-table3-1-7",
      "#vm-table3-3-1",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vms-table3-1-7",
      "#vms-table3-3-1",
      "#vms-table3-3-2",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2",
      "#vm-table3-1-1-ml",
      "#vm-table3-1-2-ml",
      "#vm-table3-1-3-ml",
      "#vm-table3-1-4-ml",
      "#vm-table3-1-5-ml",
      "#vm-table3-1-6-ml",
      "#vm-table3-1-7-ml",
      "#vm-table3-1-8-ml",
      "#vm-table3-1-9-ml",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2-ml",
      "#vm-table-sqlwindows-computingprioritization-f1-f16-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2",
      "#vm-table3-2-1-ml",
      "#vm-table3-3-1-ml",
      "#vm-sqlserverforwindows-memoryprioritization-ev3-region2-ml",
      "#vm-table3-3-2-ml",
      "#vm-table3-3-3-ml",
      "#vm-table3-3-4-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-d15v2-ml",
      "#vm-table3-3-5-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries-ml",
      "#vm-table3-4-1-ml",
      "#vm-table-Constrained-3-2",
      "#vm-table-Constrained-3-3",
      "#vm-table3-1-1",
      "#vm-table3-1-2",
      "#vm-table3-1-4",
      "#vm-table3-1-5",
      "#vm-table3-1-8",
      "#vm-table3-1-9",
      "#vm-table-sqlwindows-computingprioritization-f2sv2-f72sv2",
      "#vm-table-sqlwindows-computingprioritization-f1-f16",
      "#vm-table3-2-1",
      "#vm-table3-3-3",
      "#vm-table3-3-4",
      "#vm-table3-3-5",
      "#vm-table-sqlserverwindows-memoryprioritization-mseries"
    ]
  },
  {
    "os": "SQL Server Ubuntu Linux",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table4-1-4",
      "#vm-table4-3-1",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1",
      "#vms-table4-1-4",
      "#vms-table4-3-1",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16",
      "#vms-table4-2-1",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1-region2",
      "#vm-table4-2-1",
      "#vm-table4-3-1",
      "#vm-table4-3-2",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table4-3-3",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-3",
      "#vm-table-Constrained-4-4"
    ]
  },
  {
    "os": "SQL Server Ubuntu Linux",
    "region": "north-china",
    "tableIDs": [
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vm-table4-2-1-region2",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vms-table4-2-1-region2",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-ml",
      "#vm-table4-2-1-region2-ml",
      "#vm-table4-2-1-ml",
      "#vm-table4-3-1-ml",
      "#vm-table4-3-2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2-ml",
      "#vm-table4-3-3-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries-ml",
      "#vm-table4-1-new",
      "#vm-table4-2-new",
      "#vm-table4-3-new",
      "#vm-table4-4-new",
      "#vm-table4-5-new",
      "#vm-table4-6-new",
      "#vm-table4-7-new",
      "#vm-table4-8-new",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-2",
      "#vm-table-Constrained-4-3"
    ]
  },
  {
    "os": "SQL Server Ubuntu Linux",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table4-1-4",
      "#vm-table4-3-1",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1",
      "#vms-table4-1-4",
      "#vms-table4-3-1",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16",
      "#vms-table4-2-1",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-ml",
      "#vm-table4-2-1-region2-ml",
      "#vm-table4-2-1-ml",
      "#vm-table4-3-1-ml",
      "#vm-table4-3-2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2-ml",
      "#vm-table4-3-3-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries-ml",
      "#vm-table-Constrained-4-2",
      "#vm-table-Constrained-4-3",
      "#vm-table-Constrained-4-4"
    ]
  },
  {
    "os": "SQL Server Ubuntu Linux",
    "region": "east-china",
    "tableIDs": [
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vm-table4-2-1-region2",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vms-table4-2-1-region2",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-ml",
      "#vm-table4-2-1-region2-ml",
      "#vm-table4-2-1-ml",
      "#vm-table4-3-1-ml",
      "#vm-table4-3-2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2-ml",
      "#vm-table4-3-3-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries-ml",
      "#vm-table4-1-new",
      "#vm-table4-2-new",
      "#vm-table4-3-new",
      "#vm-table4-4-new",
      "#vm-table4-5-new",
      "#vm-table4-6-new",
      "#vm-table4-7-new",
      "#vm-table4-8-new",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-2",
      "#vm-table-Constrained-4-4"
    ]
  },
  {
    "os": "SQL Server Ubuntu Linux",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table4-1-4",
      "#vm-table4-3-1",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1",
      "#vms-table4-1-4",
      "#vms-table4-3-1",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16",
      "#vms-table4-2-1",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1-region2",
      "#vm-table4-2-1",
      "#vm-table4-3-1",
      "#vm-table4-3-2",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table4-3-3",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-3",
      "#vm-table-Constrained-4-4",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vm-table4-2-1-region2",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vms-table4-2-1-region2",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-ml",
      "#vm-table4-2-1-region2-ml",
      "#vm-table4-2-1-ml",
      "#vm-table4-3-1-ml",
      "#vm-table4-3-2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2-ml",
      "#vm-table4-3-3-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries-ml",
      "#vm-table4-1-new",
      "#vm-table4-2-new",
      "#vm-table4-3-new",
      "#vm-table4-4-new",
      "#vm-table4-5-new",
      "#vm-table4-6-new",
      "#vm-table4-7-new",
      "#vm-table4-8-new",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-2",
      "#vm-table4-1-1",
      "#vm-table4-1-2",
      "#vm-table4-1-2-1",
      "#vm-table4-1-3"
    ]
  },
  {
    "os": "SQL Server Ubuntu Linux",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table4-1-4",
      "#vm-table4-3-1",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1",
      "#vms-table4-1-4",
      "#vms-table4-3-1",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16",
      "#vms-table4-2-1",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16",
      "#vm-table4-2-1-region2",
      "#vm-table4-2-1",
      "#vm-table4-3-1",
      "#vm-table4-3-2",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table4-3-3",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-3",
      "#vm-table-Constrained-4-4",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vm-table4-2-1-region2",
      "#vms-table-sqlserverlinux-memoryprioritization-d15v2",
      "#vms-table-sqlserverlinux-memoryprioritization-ds15v2",
      "#vms-table-sqllinux-computingprioritization-f1-f16-region2",
      "#vms-table4-2-1-region2",
      "#vm-table4-1-1-ml",
      "#vm-table4-1-2-ml",
      "#vm-table4-1-3-ml",
      "#vm-table4-1-4-ml",
      "#vm-table4-1-5-ml",
      "#vm-table4-1-6-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-region2-ml",
      "#vm-table-sqllinux-computingprioritization-f1-f16-ml",
      "#vm-table4-2-1-region2-ml",
      "#vm-table4-2-1-ml",
      "#vm-table4-3-1-ml",
      "#vm-table4-3-2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-d15v2-ml",
      "#vm-table4-3-3-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-ds15v2-ml",
      "#vm-table-sqlserverlinux-memoryprioritization-mseries-ml",
      "#vm-table4-1-new",
      "#vm-table4-2-new",
      "#vm-table4-3-new",
      "#vm-table4-4-new",
      "#vm-table4-5-new",
      "#vm-table4-6-new",
      "#vm-table4-7-new",
      "#vm-table4-8-new",
      "#vm-table-Constrained-4-1",
      "#vm-table-Constrained-4-2",
      "#vm-table4-1-1",
      "#vm-table4-1-2",
      "#vm-table4-1-2-1",
      "#vm-table4-1-3"
    ]
  },
  {
    "os": "Machine Learning Server",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table5-1-4",
      "#vm-table5-3-1",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-2-1",
      "#vms-table5-1-4",
      "#vms-table5-3-1",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table-Constrained-5-2"
    ]
  },
  {
    "os": "Machine Learning Server",
    "region": "north-china",
    "tableIDs": [
      "#vm-table5-1-3",
      "#vm-table5-1-5",
      "#vm-table5-1-6",
      "#vm-table5-3-1",
      "#vm-table5-3-2",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-4-1",
      "#vm-table5-2-1-region2",
      "#vms-table5-1-3",
      "#vms-table5-1-5",
      "#vms-table5-1-6",
      "#vms-table5-3-1",
      "#vms-table5-3-2",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vms-table5-4-1",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table5-1-new",
      "#vm-table5-2-new",
      "#vm-table5-3-new",
      "#vm-table5-4-new",
      "#vm-table5-5-new",
      "#vm-table5-6-new",
      "#vm-table5-7-new",
      "#vm-table5-8-new",
      "#vm-table-Constrained-5-1"
    ]
  },
  {
    "os": "Machine Learning Server",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table5-1-4",
      "#vm-table5-3-1",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-2-1",
      "#vms-table5-1-4",
      "#vms-table5-3-1",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table-Constrained-5-2"
    ]
  },
  {
    "os": "Machine Learning Server",
    "region": "east-china",
    "tableIDs": [
      "#vm-table5-1-3",
      "#vm-table5-1-5",
      "#vm-table5-1-6",
      "#vm-table5-3-1",
      "#vm-table5-3-2",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-4-1",
      "#vm-table5-2-1-region2",
      "#vms-table5-1-3",
      "#vms-table5-1-5",
      "#vms-table5-1-6",
      "#vms-table5-3-1",
      "#vms-table5-3-2",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vms-table5-4-1",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table5-1-new",
      "#vm-table5-2-new",
      "#vm-table5-3-new",
      "#vm-table5-4-new",
      "#vm-table5-5-new",
      "#vm-table5-6-new",
      "#vm-table5-7-new",
      "#vm-table5-8-new",
      "#vm-table-Constrained-5-1"
    ]
  },
  {
    "os": "Machine Learning Server",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table5-1-3",
      "#vm-table5-1-5",
      "#vm-table5-1-6",
      "#vm-table5-3-1",
      "#vm-table5-3-2",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-4-1",
      "#vm-table5-2-1-region2",
      "#vms-table5-1-3",
      "#vms-table5-1-5",
      "#vms-table5-1-6",
      "#vms-table5-3-1",
      "#vms-table5-3-2",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vms-table5-4-1",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table5-1-new",
      "#vm-table5-2-new",
      "#vm-table5-3-new",
      "#vm-table5-4-new",
      "#vm-table5-5-new",
      "#vm-table5-6-new",
      "#vm-table5-7-new",
      "#vm-table5-8-new",
      "#vm-table-Constrained-5-1",
      "#vm-table5-1-4",
      "#vm-table5-3-1",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-2-1",
      "#vms-table5-1-4",
      "#vms-table5-3-1",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table-Constrained-5-2",
      "#vm-table5-1-1",
      "#vm-table5-1-2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms"
    ]
  },
  {
    "os": "Machine Learning Server",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table5-1-3",
      "#vm-table5-1-5",
      "#vm-table5-1-6",
      "#vm-table5-3-1",
      "#vm-table5-3-2",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-4-1",
      "#vm-table5-2-1-region2",
      "#vms-table5-1-3",
      "#vms-table5-1-5",
      "#vms-table5-1-6",
      "#vms-table5-3-1",
      "#vms-table5-3-2",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vms-table5-4-1",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table5-1-new",
      "#vm-table5-2-new",
      "#vm-table5-3-new",
      "#vm-table5-4-new",
      "#vm-table5-5-new",
      "#vm-table5-6-new",
      "#vm-table5-7-new",
      "#vm-table5-8-new",
      "#vm-table-Constrained-5-1",
      "#vm-table5-1-4",
      "#vm-table5-3-1",
      "#vm-table-machinelearning-memoryprioritization-d15v2",
      "#vm-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-2-1",
      "#vms-table5-1-4",
      "#vms-table5-3-1",
      "#vms-table-machinelearning-memoryprioritization-d15v2",
      "#vms-table-machinelearning-memoryprioritization-ds15v2",
      "#vm-table5-1-1-ml",
      "#vm-table5-1-2-ml",
      "#vm-table5-1-3-ml",
      "#vm-table5-1-4-ml",
      "#vm-table5-1-5-ml",
      "#vm-table5-1-6-ml",
      "#vm-table-machinelearning-computingprioritization-f1-f16-ml",
      "#vm-table5-2-1-ml",
      "#vm-table5-3-1-ml",
      "#vm-table5-3-2-ml",
      "#vm-table-machinelearning-memoryprioritization-d15v2-ml",
      "#vm-table5-3-3-ml",
      "#vm-table-machinelearning-memoryprioritization-ds15v2-ml",
      "#vm-table-Constrained-5-2",
      "#vm-table5-1-1",
      "#vm-table5-1-2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Basic",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-6-2",
      "#vm-table-Constrained-6-3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Basic",
    "region": "north-china",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table6-1-new",
      "#vm-table6-2-new",
      "#vm-table6-3-new",
      "#vm-table6-4-new",
      "#vm-table6-5-new",
      "#vm-table6-6-new",
      "#vm-table6-7-new",
      "#vm-table6-8-new",
      "#vm-table-Constrained-6-1",
      "#vm-table-Constrained-6-2"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Basic",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-6-2",
      "#vm-table-Constrained-6-3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Basic",
    "region": "east-china",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table6-1-new",
      "#vm-table6-2-new",
      "#vm-table6-3-new",
      "#vm-table6-4-new",
      "#vm-table6-5-new",
      "#vm-table6-6-new",
      "#vm-table6-7-new",
      "#vm-table6-8-new",
      "#vm-table-Constrained-6-1",
      "#vm-table-Constrained-6-3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Basic",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-6-2",
      "#vm-table-Constrained-6-3",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table6-1-new",
      "#vm-table6-2-new",
      "#vm-table6-3-new",
      "#vm-table6-4-new",
      "#vm-table6-5-new",
      "#vm-table6-6-new",
      "#vm-table6-7-new",
      "#vm-table6-8-new",
      "#vm-table-Constrained-6-1",
      "#vm-table-Constrained-6-3",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Basic",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-6-2",
      "#vm-table-Constrained-6-3",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-fsv2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-basic-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-basic-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table6-1-new",
      "#vm-table6-2-new",
      "#vm-table6-3-new",
      "#vm-table6-4-new",
      "#vm-table6-5-new",
      "#vm-table6-6-new",
      "#vm-table6-7-new",
      "#vm-table6-8-new",
      "#vm-table-Constrained-6-1",
      "#vm-table-Constrained-6-3",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a0-a4-basic",
      "#vm-table-suse-linux-enterprise-server-a0-a7-standard",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-a1v2-a8mv2",
      "#vm-table-suse-linux-enterprise-server-basic-generalpurpose-d2v3-d64v3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Server for SAP Priority",
    "region": "north-china2",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-7-2",
      "#vm-table-Constrained-7-3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Server for SAP Priority",
    "region": "north-china",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table7-1-new",
      "#vm-table7-2-new",
      "#vm-table7-3-new",
      "#vm-table7-4-new",
      "#vm-table7-5-new",
      "#vm-table7-6-new",
      "#vm-table7-7-new",
      "#vm-table7-8-new",
      "#vm-table-Constrained-7-1",
      "#vm-table-Constrained-7-2"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Server for SAP Priority",
    "region": "east-china2",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-7-2",
      "#vm-table-Constrained-7-3"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Server for SAP Priority",
    "region": "east-china",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table7-1-new",
      "#vm-table7-2-new",
      "#vm-table7-3-new",
      "#vm-table7-4-new",
      "#vm-table7-5-new",
      "#vm-table7-6-new",
      "#vm-table7-7-new",
      "#vm-table7-8-new",
      "#vm-table-Constrained-7-1",
      "#vm-table-Constrained-7-3"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "north-china3",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-east3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudsiervice-table-computeintensive-optimizedcompute-memoryintensive-D11-D14",
      "#cloudservice-table-generalpurpose-A0-A4",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-east3",
      "#cloudservice-table-generalpurpose-A1v2-east3-east3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2-region2",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2",
      "#cloudservice-table-memoryintensive-A5-A7",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D11-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-re",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region",
      "#cloudservice-table-computeoptimized-F2v2-F72v2"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "east-china3",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-north3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-north3",
      "#cloudsiervice-table-computeintensive-optimizedcompute-memoryintensive-D11-D14",
      "#cloudservice-table-generalpurpose-A0-A4",
      "#cloudservice-table-generalpurpose-A1v2-north3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2-region2",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2",
      "#cloudservice-table-memoryintensive-A5-A7",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D11-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region",
      "#cloudservice-table-computeoptimized-F2v2-F72v2"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Server for SAP Priority",
    "region": "east-china3",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-7-2",
      "#vm-table-Constrained-7-3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table7-1-new",
      "#vm-table7-2-new",
      "#vm-table7-3-new",
      "#vm-table7-4-new",
      "#vm-table7-5-new",
      "#vm-table7-6-new",
      "#vm-table7-7-new",
      "#vm-table7-8-new",
      "#vm-table-Constrained-7-1",
      "#vm-table-Constrained-7-3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic"
    ]
  },
  {
    "os": "SUSE Linux Enterprise Server for SAP Priority",
    "region": "north-china3",
    "tableIDs": [
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table-Constrained-7-2",
      "#vm-table-Constrained-7-3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2",
      "#vms-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-b1s-b8ms-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2sv3-d64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1-d4-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f2sv2-f72sv2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1-f16-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-computingoptimization-f1s-f16s-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2v3-e64v3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ev3-region2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-e2sv3-e64sv3-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11-d14-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds15v2-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-m32ls-m32ts-ml",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-gpu-nc6sv3-nc24rsv3-ml",
      "#vm-table7-1-new",
      "#vm-table7-2-new",
      "#vm-table7-3-new",
      "#vm-table7-4-new",
      "#vm-table7-5-new",
      "#vm-table7-6-new",
      "#vm-table7-7-new",
      "#vm-table7-8-new",
      "#vm-table-Constrained-7-1",
      "#vm-table-Constrained-7-3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-ds11v2-ds14v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-memoryprioritization-d11v2-d15v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-ds1v2-ds5v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d1v2-d5v2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-d2v3-d64v3",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a1v2-a8mv2",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a7-standard",
      "#vm-table-suse-linux-enterprise-server-for-sap-priority-generalpurpose-a0-a4-basic"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "north-china3",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-east3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-east3",
      "#cloudservice-table-generalpurpose-A1v2-east3-east3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2-region2",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2",
      "#cloudservice-table-memoryintensive-A5-A7",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D11-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-re",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region",
      "#cloudservice-table-computeoptimized-F2v2-F72v2"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "east-china3",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-north3",
      "#cloudservice-table-generalpurpose-A1v2-north3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2-region2",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2",
      "#cloudservice-table-memoryintensive-A5-A7",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D11-D4",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region",
      "#cloudservice-table-computeoptimized-F2v2-F72v2"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "north-china2",
    "tableIDs": [
      "#cloudservice-table-generalpurpose-A1v2-A8mv2",
      "#cloudservice-table-generalpurpose-A1v2-east3-east3",
      "#cloudservice-table-generalpurpose-A1v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1-D4",
      "#cloudsiervice-table-computeintensive-optimizedcompute-memoryintensive-D11-D14",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-east3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-east3"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "north-china",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-east3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-east3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2-region2",
      "#cloudservice-table-generalpurpose-A1v2-east3-east3",
      "#cloudservice-table-generalpurpose-A1v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "east-china2",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-east3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-east3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2",
      "#cloudservice-table-generalpurpose-A1v2-east3-east3",
      "#cloudservice-table-generalpurpose-A1v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1-D4",
      "#cloudsiervice-table-computeintensive-optimizedcompute-memoryintensive-D11-D14",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-region",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-region"
    ]
  },
  {
    "os": "Cloud Services",
    "region": "east-china",
    "tableIDs": [
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-north3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D1v2-D5v2-east3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-D11v2-D15v2-east3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3-east3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2-east3",
      "#cloudservice-table-generalpurpose-A1v2-A8mv2-region2",
      "#cloudservice-table-generalpurpose-A1v2-east3-east3",
      "#cloudservice-table-generalpurpose-A1v2-north3",
      "#cloudservice-table-computeintensive-optimizedcompute-generalpurpose-D2v3-D64v3",
      "#cloudservice-table-optimizedcompute-memoryintensive-E2v3-E64v3",
      "#cloudservice-table-computeoptimized-F2v2-F72v2"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "east-china",
    "tableIDs": [
      "#T2-G-P",
      "#T2-G-PM",
      "#T3-G-P",
      "#T3-G-PM",
      "#T2-B-P",
      "#T2-B-PM",
      "#T3-B-P",
      "#T3-B-PM",
      "#Managed_Instance_area-5",
      "#sqldb-managed-instance-general-purpose-gen5",
      "#sqldb-managed-instance-general-purpose-gen5-1",
      "#sqldb-managed-instance-business-critical-gen5",
      "#sqldb-managed-instance-business-premium-serries-2",
      "#sqldb-managed-instance-business-premium-serries-2-yh"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "north-china",
    "tableIDs": [
      "#T2-G-P",
      "#T2-G-PM",
      "#T3-G-P",
      "#T3-G-PM",
      "#T2-B-P",
      "#T2-B-PM",
      "#T3-B-P",
      "#T3-B-PM",
      "#Managed_Instance_area-5",
      "#sqldb-managed-instance-general-purpose-gen5",
      "#sqldb-managed-instance-general-purpose-gen5-1",
      "#sqldb-managed-instance-business-critical-gen5",
      "#sqldb-managed-instance-business-premium-serries-2",
      "#sqldb-managed-instance-business-premium-serries-2-yh"
    ]
  },
  {
    "os": "Azure Defender",
    "region": "east-china",
    "tableIDs": [
      "#azure-defender-pro"
    ]
  },
  {
    "os": "Azure Defender",
    "region": "north-china",
    "tableIDs": [
      "#azure-defender-pro"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "east-china",
    "tableIDs": [
      "#T2-G-P",
      "#T2-G-PM",
      "#T3-G-P",
      "#T3-G-PM",
      "#T2-B-P",
      "#T2-B-PM",
      "#T3-B-P",
      "#T3-B-PM",
      "#Managed_Instance_area-5",
      "#sqldb-managed-instance-general-purpose-gen5",
      "#sqldb-managed-instance-general-purpose-gen5-1",
      "#sqldb-managed-instance-business-critical-gen5",
      "#SQL_General_Gen5_11_N3",
      "#SQL_General_Gen5_11_E3",
      "#MI_General_Purpose_Storage_14_N3",
      "#MI_General_Purpose_Storage_14_E3",
      "#MI_2-1_Point_N3",
      "#MI_2-1_Point_E3",
      "#MI_2_Long_N3",
      "#MI_2_Long_E3",
      "#MI_Business_Critical_Gen5_16_N3",
      "#MI_Business_Critical_Gen5_16_E3",
      "#MI_Business_Critical_Storage_15_N3",
      "#MI_Business_Critical_Storage_15_E3",
      "#MI_2-2_Point_N3",
      "#MI_2-2_Point_E3",
      "#Managed_Instance_Premium-series-2"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "north-china",
    "tableIDs": [
      "#T2-G-P",
      "#T2-G-PM",
      "#T3-G-P",
      "#T3-G-PM",
      "#T2-B-P",
      "#T2-B-PM",
      "#T3-B-P",
      "#T3-B-PM",
      "#Managed_Instance_area-5",
      "#sqldb-managed-instance-general-purpose-gen5",
      "#sqldb-managed-instance-general-purpose-gen5-1",
      "#sqldb-managed-instance-business-critical-gen5",
      "#SQL_General_Gen5_11_N3",
      "#SQL_General_Gen5_11_E3",
      "#MI_General_Purpose_Storage_14_N3",
      "#MI_General_Purpose_Storage_14_E3",
      "#MI_2-1_Point_N3",
      "#MI_2-1_Point_E3",
      "#MI_2_Long_N3",
      "#MI_2_Long_E3",
      "#MI_Business_Critical_Gen5_16_N3",
      "#MI_Business_Critical_Gen5_16_E3",
      "#MI_Business_Critical_Storage_15_N3",
      "#MI_Business_Critical_Storage_15_E3",
      "#Elastic_Database_NE3_5-east3",
      "#MI_2-2_Point_N3",
      "#MI_2-2_Point_E3",
      "#Managed_Instance_Premium-series-2"
    ]
  },
  {
    "os": "Elastic Database",
    "region": "east-china",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen5",
      "#sqldb-elastic-vcore-business-critical-gen5",
      "#Elastic_General_Purpose_Gen5_17_N3",
      "#Elastic_General_Purpose_Gen5_17_E3",
      "#Elastic_General_Fscv2_18_N3",
      "#Elastic_General_Fscv2_18_E3",
      "#Single_database_NE3_10_east3",
      "#Elastic_Database_NE3_4_east3",
      "#Elastic_General_Storage_9_N3",
      "#Elastic_General_Storage_9_E3",
      "#elastic_1-1_Point_N3",
      "#elastic_1-1_Long_N3",
      "#elastic_1-1_Long_E3",
      "#Elastic_Business_Critical_Gen5_19_N3",
      "#Elastic_Business_Critical_Gen5_19_E3",
      "#Elastic_Database_NE3_8_east3",
      "#Elastic_Business_M_20_N3",
      "#Elastic_Business_M_20_E3",
      "#elastic_1-2_Point_N3",
      "#elastic_1-2_Point_E3",
      "#elastic_1-2_Long_N3",
      "#Elastic_Database_NE3_5_east3",
      "#elastic_1-2_Long_E3",
      "#Elastic_6_Elastic_Pools_Basic_N3",
      "#Elastic_6_Elastic_Pools_Basic_E3",
      "#Elastic_7_Elastic_Pools_Standard_N3",
      "#Elastic_7_Elastic_Pools_Standard_E3",
      "#Elastic_8_Elastic_Pools_Premium_N3",
      "#Elastic_8_Elastic_Pools_Premium_E3",
      "#Elastic_Pool_Extra_Data_Storage_12_N3",
      "#Elastic_Pool_Extra_Data_Storage_12_E3",
      "#elastic_1-3_Long_N3",
      "#elastic_1-3_Long_E3","#sqldb-elastic-vcore-general-purpose-gen4-n3",
      "#Managed_Instance_standard-area-2-n3",
      "#Elastic_General_Storage_9_N3-t",
      "#elastic_1-1_Point_N3-t",
      "#elastic_1-1_Long_N3-t",
      "#Elastic_Database_NE3_4-t",
      "#Elastic_Business_Critical_Gen5_19_N3-t",
      "#Elastic_Database_NE3_5-t",
      "#elastic_1-2_Point_N3-t",
      "#elastic_1-2_Long_N3-t",
      "#Elastic_Database_NE3_8-t",
      "#Elastic_6_Elastic_Pools_Basic_N3-t",
      "#Elastic_7_Elastic_Pools_Standard_N3-t",
      "#Elastic_8_Elastic_Pools_Premium_N3-t",
      "#Elastic_Database_NE3_12-t",
      "#elastic_1-3_Long_N3-t",
      "#sqldb-elastic-vcore-general-purpose-gen4-n3",
      "#Managed_Instance_standard-area-2-n3",
      "#Elastic_General_Storage_9_N3-t",
      "#elastic_1-1_Point_N3-t",
      "#elastic_1-1_Long_N3-t",
      "#Elastic_Database_NE3_4-t",
      "#Elastic_Business_Critical_Gen5_19_N3-t",
      "#Elastic_Database_NE3_5-t",
      "#elastic_1-2_Point_N3-t",
      "#elastic_1-2_Long_N3-t",
      "#Elastic_Database_NE3_8-t",
      "#Elastic_6_Elastic_Pools_Basic_N3-t",
      "#Elastic_7_Elastic_Pools_Standard_N3-t",
      "#Elastic_8_Elastic_Pools_Premium_N3-t",
      "#Elastic_Database_NE3_12-t",
      "#elastic_1-3_Long_N3-t",
      "#Managed_Instance_Premium-series-2",
      "#Standard-series-gen5-preview-n3-elastic",
      "#Standard-series-primary-n2-elastic",
      "#Standard-series-preview-storage-n3-elastic",
      "#Elastic_Hyperscale_Gen5_24_N3",
      "#Elastic_Hyperscale_Gen5_24_E3",
      "#sqldb-elastic-vcore-hyperscale-gen5",
      "#sqldb-single-vcore-hyperscale-gen5",
      "#Elastic_database_N3_2_a",
      "#Elastic_1-1_Long_N3_a",
      "#Elastic_database_N3_2_a",
      "#Standard-series-primary-n3-elastic",
      "#Elastic_Database_NE3_2",
      "#Elastic_Database_NE4",
      "#elastic_1-4_Point_N3",
      "#elastic_1-4_Long_N3"
    ]
  },
  {
    "os": "Elastic Database",
    "region": "north-china",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen5",
      "#sqldb-elastic-vcore-business-critical-gen5",
      "#Elastic_General_Purpose_Gen5_17_N3",
      "#Elastic_Database_NE3_5",
      "#Elastic_General_Purpose_Gen5_17_E3",
      "#Elastic_General_Fscv2_18_N3",
      "#Elastic_General_Fscv2_18_E3",
      "#Single_database_NE3_10_east3",
      "#Elastic_Database_NE3_4_east3",
      "#Elastic_Database_NE3_8_east3",
      "#Elastic_General_Storage_9_N3",
      "#Elastic_Database_NE3_5-east3",
      "#Elastic_General_Storage_9_E3",
      "#elastic_1-1_Point_N3",
      "#elastic_1-1_Long_N3",
      "#Elastic_Database_NE3_5_east3",
      "#elastic_1-1_Long_E3",
      "#Elastic_Business_Critical_Gen5_19_N3",
      "#Elastic_Business_Critical_Gen5_19_E3",
      "#Elastic_Business_M_20_N3",
      "#Elastic_Business_M_20_E3",
      "#elastic_1-2_Point_N3",
      "#elastic_1-2_Point_E3",
      "#elastic_1-2_Long_N3",
      "#elastic_1-2_Long_E3",
      "#Elastic_6_Elastic_Pools_Basic_N3",
      "#Elastic_6_Elastic_Pools_Basic_E3",
      "#Elastic_7_Elastic_Pools_Standard_N3",
      "#Elastic_7_Elastic_Pools_Standard_E3",
      "#Elastic_8_Elastic_Pools_Premium_N3",
      "#Elastic_8_Elastic_Pools_Premium_E3",
      "#Elastic_Pool_Extra_Data_Storage_12_N3",
      "#Elastic_Pool_Extra_Data_Storage_12_E3",
      "#elastic_1-3_Long_N3",
      "#elastic_1-3_Long_E3",
      "#sqldb-elastic-vcore-general-purpose-gen4-n3",
      "#Managed_Instance_standard-area-2-n3",
      "#Elastic_General_Storage_9_N3-t",
      "#elastic_1-1_Point_N3-t",
      "#elastic_1-1_Long_N3-t",
      "#Elastic_Database_NE3_4-t",
      "#Elastic_Business_Critical_Gen5_19_N3-t",
      "#Elastic_Database_NE3_5-t",
      "#elastic_1-2_Point_N3-t",
      "#elastic_1-2_Long_N3-t",
      "#Elastic_Database_NE3_8-t",
      "#Elastic_6_Elastic_Pools_Basic_N3-t",
      "#Elastic_7_Elastic_Pools_Standard_N3-t",
      "#Elastic_8_Elastic_Pools_Premium_N3-t",
      "#Elastic_Database_NE3_12-t",
      "#elastic_1-3_Long_N3-t",
      "#Managed_Instance_Premium-series-2",
      "#Standard-series-gen5-preview-n3-elastic",
      "#Standard-series-primary-n2-elastic",
      "#Standard-series-preview-storage-n3-elastic",
      "#Elastic_Hyperscale_Gen5_24_N3",
      "#Elastic_Hyperscale_Gen5_24_E3",
      "#sqldb-elastic-vcore-hyperscale-gen5",
      "#sqldb-single-vcore-hyperscale-gen5",
      "#Elastic_database_N3_2_a",
      "#Elastic_1-1_Long_N3_a",
      "#Elastic_database_N3_2_a",
      "#Standard-series-primary-n3-elastic",
      "#Elastic_Database_NE4",
      "#elastic_1-4_Point_N3",
      "#elastic_1-4_Long_N3"
    ]
  },
  {
    "os": "Single database",
    "region": "east-china",
    "tableIDs": [
      "#sqldb-single-vcore-general-purpose-gen5",
      "#sqldb-single-vcore-general-purpose-gen5_a",
      "#sqldb-single-vcore-business-critical-gen5",
      "#sqldb-single-vcore-hyperscale-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5_a",
      "#single_1-1_Point_N3",
      "#single_1-1_Point_N3_a",
      "#single_1-1_Point_E3",
      "#single_1-1_Point_E3_a",
      "#single_1-1_Long_N3",
      "#single_1-1_Long_N3_a",
      "#single_1-1_Long_E3",
      "#single_1-1_Long_E3_a",
      "#Single_General_Purpose_Gen5_17_N3",
      "#Single_General_Purpose_Gen5_17_N3_a",
      "#Single_General_Purpose_Gen5_17_E3",
      "#Single_General_Purpose_Gen5_17_E3_a",
      "#Single_General_Fscv2_18_N3",
      "#Single_General_Fscv2_18_E3",
      "#Single_General_Storage_9_N3",
      "#Single_General_Storage_9_E3",
      "#single_1-2_Point_N3",
      "#single_1-2_Point_E3",
      "#single_1-2_Long_N3",
      "#Single_General_IO_10_N3",
      "#Single_General_IO_10_E3",
      "#Standard-series-gen5-preview-n3",
      "#Standard-series-gen5-preview-n3_a",
      "#Standard-series-primary-n3",
      "#Standard-series-primary-n3_a",
      "#Standard-series-preview-storage-n3",
      "#Standard-series-preview-storage-n3_a",
      "#Single_Business_Critical_Gen5_19_N3",
      "#Single_Business_Critical_Gen5_19_E3",
      "#Single_Business_M_20_N3",
      "#Single_Business_M_20_E3",
      "#Single_Business_Critical_Storage_13_N3",
      "#Single_Business_Critical_Storage_13_E3",
      "#single_1-3_Point_N3",
      "#single_1-3_Point_E3",
      "#single_1-3_Long_N3",
      "#single_1-3_Long_E3",
      "#Single_Hyperscale_Gen5_24_N3",
      "#Single_Hyperscale_Gen5_24_E3",
      "#Single_Hyperscale_storage_21_N3",
      "#Single_Hyperscale_storage_21_E3",
      "#single_1-4_Point_N3",
      "#single_1-4_Point_E3",
      "#Single_Hyperscale_IOs_22_N3",
      "#Single_Hyperscale_IOs_22_E3",
      "#Single_5_basic_N3",
      "#Single_5_basic_E3",
      "#Single_3_Dtu_Premium_N3",
      "#Single_3_Dtu_Premium_E3",
      "#single_1-4_Long_N3",
      "#single_1-4_Long_E3",
      "#Managed_Instance_standard-area-1-t",
      "#single_1-1_Point_N3-t",
      "#single_1-1_Long_N3-t",
      "#Managed_Instance_standard-area-2-t",
      "#Single_General_Storage_9_N3-t",
      "#single_1-2_Point_N3-t",
      "#single_1-2_Long_N3-t",
      "#Single_General_IO_10_N3-t",
      "#Single_Business_Critical_Gen5_19_N3-t",
      "#Single_Business_Critical_Storage_13_N3-t",
      "#single_1-3_Point_N3-t",
      "#single_1-3_Long_N3-t",
      "#Single_Hyperscale_Gen5_24_N3-t",
      "#Single_Hyperscale_storage_21_N3-t",
      "#single_1-4_Point_N3-t",
      "#Single_Hyperscale_IOs_22_N3-t",
      "#Single_5_basic_N3-t",
      "#Single_database_NE3_15-t",
      "#single_1-4_Long_N3-t",
      "#Single_3_Dtu_Premium_N3-t",
      "#sqldb-elastic-vcore-hyperscale-gen5"
    ]
  },
  {
    "os": "Single database",
    "region": "north-china",
    "tableIDs": [
      "#sqldb-single-vcore-general-purpose-gen5",
      "#sqldb-single-vcore-general-purpose-gen5_a",
      "#sqldb-single-vcore-business-critical-gen5",
      "#sqldb-single-vcore-hyperscale-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5_a",
      "#single_1-1_Point_N3",
      "#single_1-1_Point_N3_a",
      "#single_1-1_Point_E3",
      "#single_1-1_Point_E3_a",
      "#single_1-1_Long_N3",
      "#single_1-1_Long_N3_a",
      "#single_1-1_Long_E3",
      "#single_1-1_Long_E3_a",
      "#Single_General_Purpose_Gen5_17_N3",
      "#Single_General_Purpose_Gen5_17_N3_a",
      "#Single_General_Purpose_Gen5_17_E3",
      "#Single_General_Purpose_Gen5_17_E3_a",
      "#Single_General_Fscv2_18_N3",
      "#Single_General_Fscv2_18_E3",
      "#Single_General_Storage_9_N3",
      "#Single_General_Storage_9_E3",
      "#single_1-2_Point_N3",
      "#single_1-2_Point_E3",
      "#single_1-2_Long_N3",
      "#Single_General_IO_10_N3",
      "#Single_General_IO_10_E3",
      "#Single_Business_Critical_Gen5_19_N3",
      "#Single_Business_Critical_Gen5_19_E3",
      "#Single_Business_M_20_N3",
      "#Single_Business_M_20_E3",
      "#Single_Business_Critical_Storage_13_N3",
      "#Single_Business_Critical_Storage_13_E3",
      "#single_1-3_Point_N3",
      "#single_1-3_Point_E3",
      "#single_1-3_Long_N3",
      "#single_1-3_Long_E3",
      "#Single_Hyperscale_Gen5_24_N3",
      "#Single_Hyperscale_Gen5_24_E3",
      "#Single_Hyperscale_storage_21_N3",
      "#Single_Hyperscale_storage_21_E3",
      "#single_1-4_Point_N3",
      "#single_1-4_Point_E3",
      "#Single_Hyperscale_IOs_22_N3",
      "#Single_Hyperscale_IOs_22_E3",
      "#Single_5_basic_N3",
      "#Single_5_basic_E3",
      "#Single_3_Dtu_Premium_N3",
      "#Single_3_Dtu_Premium_E3",
      "#single_1-4_Long_N3",
      "#single_1-4_Long_E3",
      "#Managed_Instance_standard-area-1-t",
      "#single_1-1_Point_N3-t",
      "#single_1-1_Long_N3-t",
      "#Managed_Instance_standard-area-2-t",
      "#Single_General_Storage_9_N3-t",
      "#single_1-2_Point_N3-t",
      "#single_1-2_Long_N3-t",
      "#Single_General_IO_10_N3-t",
      "#Single_Business_Critical_Gen5_19_N3-t",
      "#Single_Business_Critical_Storage_13_N3-t",
      "#single_1-3_Point_N3-t",
      "#single_1-3_Long_N3-t",
      "#Single_Hyperscale_Gen5_24_N3-t",
      "#Single_Hyperscale_storage_21_N3-t",
      "#single_1-4_Point_N3-t",
      "#Single_Hyperscale_IOs_22_N3-t",
      "#Single_5_basic_N3-t",
      "#Single_database_NE3_15-t",
      "#single_1-4_Long_N3-t",
      "#Single_3_Dtu_Premium_N3-t",
      "#Standard-series-gen5-preview-n3",
      "#Standard-series-gen5-preview-n3_a",
      "#Standard-series-primary-n3",
      "#Standard-series-primary-n3_a",
      "#Standard-series-preview-storage-n3",
      "#Standard-series-preview-storage-n3_a",
      "#sqldb-elastic-vcore-hyperscale-gen5",
      "#Single_database_NE3_2_1_a",
      "#Single_database_NE3_5",
      "#Single_database_NE3_2_a"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "east-china2",
    "tableIDs": [
      "#T3-G-P",
      "#T3-G-PM",
      "#T3-B-P",
      "#T3-B-PM",
      "#Managed_Instance_area-5",
      "#SQL_General_Gen5_11_N3",
      "#SQL_General_Gen5_11_E3",
      "#sqldb-managed-instance-general-purpose-gen5-1",
      "#MI_General_Purpose_Storage_14_N3",
      "#MI_General_Purpose_Storage_14_E3",
      "#MI_2-1_Point_N3",
      "#MI_2-1_Point_E3",
      "#MI_2_Long_E3",
      "#MI_Business_Critical_Gen5_16_N3",
      "#MI_Business_Critical_Gen5_16_E3",
      "#MI_Business_Critical_Storage_15_N3",
      "#MI_Business_Critical_Storage_15_E3",
      "#MI_2-2_Point_N3",
      "#MI_2-2_Point_E3"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "north-china2",
    "tableIDs": [
      "#T3-G-P",
      "#T3-G-PM",
      "#T3-B-P",
      "#T3-B-PM",
      "#Managed_Instance_area-5",
      "#SQL_General_Gen5_11_N3",
      "#sqldb-managed-instance-general-purpose-gen5-1",
      "#SQL_General_Gen5_11_E3",
      "#MI_General_Purpose_Storage_14_N3",
      "#MI_General_Purpose_Storage_14_E3",
      "#MI_2-1_Point_N3",
      "#MI_2-1_Point_E3",
      "#MI_2_Long_E3",
      "#MI_Business_Critical_Gen5_16_N3",
      "#MI_Business_Critical_Gen5_16_E3",
      "#MI_Business_Critical_Storage_15_N3",
      "#MI_Business_Critical_Storage_15_E3",
      "#MI_2-2_Point_N3",
      "#MI_2-2_Point_E3"
    ]
  },
  {
    "os": "Elastic Database",
    "region": "east-china2",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#Elastic_General_Purpose_Gen5_17_N3",
      "#Elastic_General_Purpose_Gen5_17_E3",
      "#Elastic_General_Fscv2_18_N3",
      "#Elastic_General_Fscv2_18_E3",
      "#Elastic_General_Storage_9_N3",
      "#Elastic_Database_NE3_8_east3",
      "#Elastic_Database_NE3_5-east3",
      "#Elastic_Database_NE3_4_east3",
      "#Single_database_NE3_10_east3",
      "#Elastic_General_Storage_9_E3",
      "#Elastic_Database_NE3_5_east3",
      "#elastic_1-1_Point_N3",
      "#elastic_1-1_Long_N3",
      "#elastic_1-1_Long_E3",
      "#Elastic_Business_Critical_Gen5_19_N3",
      "#Elastic_Business_Critical_Gen5_19_E3",
      "#Elastic_Business_M_20_N3",
      "#Elastic_Business_M_20_E3",
      "#elastic_1-2_Point_N3",
      "#elastic_1-2_Long_N3",
      "#Elastic_6_Elastic_Pools_Basic_N3",
      "#Elastic_6_Elastic_Pools_Basic_E3",
      "#Elastic_7_Elastic_Pools_Standard_N3",
      "#Elastic_7_Elastic_Pools_Standard_E3",
      "#Elastic_8_Elastic_Pools_Premium_N3",
      "#Elastic_8_Elastic_Pools_Premium_E3",
      "#Elastic_Pool_Extra_Data_Storage_12_N3",
      "#Elastic_Pool_Extra_Data_Storage_12_E3",
      "#elastic_1-3_Long_N3",
      "#elastic_1-3_Long_E3",
      "#sqldb-elastic-vcore-general-purpose-gen4-n3",
      "#Managed_Instance_standard-area-2-n3",
      "#Elastic_General_Storage_9_N3-t",
      "#elastic_1-1_Point_N3-t",
      "#elastic_1-1_Long_N3-t",
      "#Elastic_Database_NE3_4-t",
      "#Elastic_Business_Critical_Gen5_19_N3-t",
      "#Elastic_Database_NE3_5-t",
      "#elastic_1-2_Point_N3-t",
      "#elastic_1-2_Long_N3-t",
      "#Elastic_Database_NE3_8-t",
      "#Elastic_6_Elastic_Pools_Basic_N3-t",
      "#Elastic_7_Elastic_Pools_Standard_N3-t",
      "#Elastic_8_Elastic_Pools_Premium_N3-t",
      "#Elastic_Database_NE3_12-t",
      "#elastic_1-3_Long_N3-t",
      "#Standard-series-gen5-preview-n3-elastic",
      "#Standard-series-primary-n2-elastic",
      "#Standard-series-preview-storage-n3-elastic",
      "#Elastic_Hyperscale_Gen5_24_N3",
      "#Elastic_Hyperscale_Gen5_24_E3",
      "#Elastic_database_N3_2_a",
      "#Elastic_1-1_Long_N3_a",
      "#Elastic_database_N3_2_a",
      "#Standard-series-primary-n3-elastic",
      "#Elastic_Database_NE3_2",
      "#Elastic_Database_NE4",
      "#elastic_1-4_Point_N3",
      "#elastic_1-4_Long_N3",
      "#Elastic_Database_NE3_7"
    ]
  },
  {
    "os": "Elastic Database",
    "region": "north-china2",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#Elastic_General_Purpose_Gen5_17_N3",
      "#Elastic_General_Purpose_Gen5_17_E3",
      "#Elastic_General_Fscv2_18_N3",
      "#Elastic_General_Fscv2_18_E3",
      "#Elastic_General_Storage_9_N3",
      "#Elastic_General_Storage_9_E3",
      "#Elastic_Database_NE3_5-east3",
      "#Elastic_Database_NE3_5_east3",
      "#Elastic_Database_NE3_8_east3",
      "#Elastic_Database_NE3_4_east3",
      "#Single_database_NE3_10_east3",
      "#elastic_1-1_Long_E3",
      "#Elastic_Business_Critical_Gen5_19_N3",
      "#Elastic_Business_Critical_Gen5_19_E3",
      "#Elastic_Business_M_20_N3",
      "#Elastic_Business_M_20_E3",
      "#Elastic_6_Elastic_Pools_Basic_N3",
      "#Elastic_6_Elastic_Pools_Basic_E3",
      "#Elastic_7_Elastic_Pools_Standard_N3",
      "#Elastic_7_Elastic_Pools_Standard_E3",
      "#Elastic_8_Elastic_Pools_Premium_N3",
      "#Elastic_8_Elastic_Pools_Premium_E3",
      "#Elastic_Pool_Extra_Data_Storage_12_N3",
      "#Elastic_Pool_Extra_Data_Storage_12_E3",
      "#sqldb-elastic-vcore-general-purpose-gen4-n3",
      "#Managed_Instance_standard-area-2-n3",
      "#Elastic_General_Storage_9_N3-t",
      "#elastic_1-1_Point_N3-t",
      "#elastic_1-1_Long_N3-t",
      "#Elastic_Database_NE3_4-t",
      "#Elastic_Business_Critical_Gen5_19_N3-t",
      "#Elastic_Database_NE3_5-t",
      "#elastic_1-2_Point_N3-t",
      "#elastic_1-2_Long_N3-t",
      "#Elastic_Database_NE3_8-t",
      "#Elastic_6_Elastic_Pools_Basic_N3-t",
      "#Elastic_7_Elastic_Pools_Standard_N3-t",
      "#Elastic_8_Elastic_Pools_Premium_N3-t",
      "#Elastic_Database_NE3_12-t",
      "#elastic_1-3_Long_N3-t",
      "#Standard-series-gen5-preview-n3-elastic",
      "#Standard-series-primary-n2-elastic",
      "#Standard-series-preview-storage-n3-elastic",
      "#Elastic_Hyperscale_Gen5_24_N3",
      "#Elastic_Hyperscale_Gen5_24_E3",
      "#Elastic_database_N3_2_a",
      "#Elastic_1-1_Long_N3_a",
      "#Elastic_database_N3_2_a",
      "#Standard-series-primary-n3-elastic",
      "#Elastic_Database_NE3_2",
      "#elastic_1-2_Point_N3",
      "#elastic_1-2_Point_E3",
      "#elastic_1-2_Long_N3",
      "#elastic_1-2_Long_E3",
      "#elastic_1-1_Point_N3",
      "#elastic_1-1_Long_N3",
      "#elastic_1-3_Long_N3",
      "#elastic_1-3_Long_E3",
      "#Elastic_Database_NE4",
      "#elastic_1-4_Long_N3",
      "#elastic_1-4_Point_N3",
      "#Elastic_Database_NE3_7"
    ]
  },
  {
    "os": "Single database",
    "region": "east-china2",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#single_1-1_Point_N3",
      "#single_1-1_Point_N3_a",
      "#single_1-1_Point_E3",
      "#single_1-1_Point_E3_a",
      "#single_1-1_Long_N3",
      "#single_1-1_Long_N3_a",
      "#single_1-1_Long_E3",
      "#single_1-1_Long_E3_a",
      "#Single_General_Purpose_Gen5_17_N3",
      "#Single_General_Purpose_Gen5_17_N3_a",
      "#Single_General_Purpose_Gen5_17_E3",
      "#Single_General_Purpose_Gen5_17_E3_a",
      "#Single_General_Fscv2_18_N3",
      "#Single_General_Fscv2_18_E3",
      "#Single_General_Storage_9_N3",
      "#Single_General_Storage_9_E3",
      "#single_1-2_Point_N3",
      "#single_1-2_Point_E3",
      "#single_1-2_Long_N3",
      "#Single_General_IO_10_N3",
      "#Single_General_IO_10_E3",
      "#Single_Business_Critical_Gen5_19_N3",
      "#Single_Business_Critical_Gen5_19_E3",
      "#Single_Business_M_20_N3",
      "#Single_Business_M_20_E3",
      "#Single_Business_Critical_Storage_13_N3",
      "#Single_Business_Critical_Storage_13_E3",
      "#single_1-3_Point_N3",
      "#single_1-3_Point_E3",
      "#single_1-3_Long_N3",
      "#single_1-3_Long_E3",
      "#Single_Hyperscale_Gen5_24_N3",
      "#Single_Hyperscale_Gen5_24_E3",
      "#Single_Hyperscale_storage_21_N3",
      "#Single_Hyperscale_storage_21_E3",
      "#single_1-4_Point_N3",
      "#single_1-4_Point_E3",
      "#Single_Hyperscale_IOs_22_N3",
      "#Single_Hyperscale_IOs_22_E3",
      "#Single_5_basic_N3",
      "#Single_5_basic_E3",
      "#Single_3_Dtu_Premium_N3",
      "#Single_3_Dtu_Premium_E3",
      "#single_1-4_Long_N3",
      "#single_1-4_Long_E3",
      "#Managed_Instance_standard-area-1-t",
      "#single_1-1_Point_N3-t",
      "#single_1-1_Long_N3-t",
      "#Managed_Instance_standard-area-2-t",
      "#Single_General_Storage_9_N3-t",
      "#single_1-2_Point_N3-t",
      "#single_1-2_Long_N3-t",
      "#Single_General_IO_10_N3-t",
      "#Single_Business_Critical_Gen5_19_N3-t",
      "#Single_Business_Critical_Storage_13_N3-t",
      "#single_1-3_Point_N3-t",
      "#single_1-3_Long_N3-t",
      "#Single_Hyperscale_Gen5_24_N3-t",
      "#Single_Hyperscale_storage_21_N3-t",
      "#single_1-4_Point_N3-t",
      "#Single_Hyperscale_IOs_22_N3-t",
      "#Single_5_basic_N3-t",
      "#Single_database_NE3_15-t",
      "#single_1-4_Long_N3-t",
      "#Single_3_Dtu_Premium_N3-t",
      "#Standard-series-gen5-preview-n3",
      "#Standard-series-gen5-preview-n3_a",
      "#Standard-series-primary-n3",
      "#Standard-series-primary-n3_a",
      "#Standard-series-preview-storage-n3",
      "#Standard-series-preview-storage-n3_a",
      "#sqldb-single-vcore-serverless-compute-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5_a",
      "#Single_database_NE3_12"
    ]
  },
  {
    "os": "Single database",
    "region": "north-china2",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#single_1-1_Point_N3",
      "#single_1-1_Point_N3_a",
      "#single_1-1_Point_E3",
      "#single_1-1_Point_E3_a",
      "#single_1-1_Long_N3",
      "#single_1-1_Long_N3_a",
      "#single_1-1_Long_E3",
      "#single_1-1_Long_E3_a",
      "#Single_General_Purpose_Gen5_17_N3",
      "#Single_General_Purpose_Gen5_17_N3_a",
      "#Single_General_Purpose_Gen5_17_E3",
      "#Single_General_Purpose_Gen5_17_E3_a",
      "#Single_General_Fscv2_18_N3",
      "#Single_General_Fscv2_18_E3",
      "#Single_General_Storage_9_N3",
      "#Single_General_Storage_9_E3",
      "#single_1-2_Point_N3",
      "#single_1-2_Point_E3",
      "#single_1-2_Long_N3",
      "#Single_General_IO_10_N3",
      "#Single_General_IO_10_E3",
      "#Single_Business_Critical_Gen5_19_N3",
      "#Single_Business_Critical_Gen5_19_E3",
      "#Single_Business_M_20_N3",
      "#Single_Business_M_20_E3",
      "#Single_Business_Critical_Storage_13_N3",
      "#Single_Business_Critical_Storage_13_E3",
      "#single_1-3_Point_N3",
      "#single_1-3_Point_E3",
      "#single_1-3_Long_N3",
      "#single_1-3_Long_E3",
      "#Single_Hyperscale_Gen5_24_N3",
      "#Single_Hyperscale_Gen5_24_E3",
      "#Single_Hyperscale_storage_21_N3",
      "#Single_Hyperscale_storage_21_E3",
      "#single_1-4_Point_N3",
      "#single_1-4_Point_E3",
      "#Single_Hyperscale_IOs_22_N3",
      "#Single_Hyperscale_IOs_22_E3",
      "#Single_5_basic_N3",
      "#Single_5_basic_E3",
      "#Single_3_Dtu_Premium_N3",
      "#Single_3_Dtu_Premium_E3",
      "#single_1-4_Long_N3",
      "#single_1-4_Long_E3",
      "#Managed_Instance_standard-area-1-t",
      "#single_1-1_Point_N3-t",
      "#single_1-1_Long_N3-t",
      "#Managed_Instance_standard-area-2-t",
      "#Single_General_Storage_9_N3-t",
      "#single_1-2_Point_N3-t",
      "#single_1-2_Long_N3-t",
      "#Single_General_IO_10_N3-t",
      "#Single_Business_Critical_Gen5_19_N3-t",
      "#Single_Business_Critical_Storage_13_N3-t",
      "#single_1-3_Point_N3-t",
      "#single_1-3_Long_N3-t",
      "#Single_Hyperscale_Gen5_24_N3-t",
      "#Single_Hyperscale_storage_21_N3-t",
      "#single_1-4_Point_N3-t",
      "#Single_Hyperscale_IOs_22_N3-t",
      "#Single_5_basic_N3-t",
      "#Single_database_NE3_15-t",
      "#single_1-4_Long_N3-t",
      "#Single_3_Dtu_Premium_N3-t",
      "#Standard-series-gen5-preview-n3",
      "#Standard-series-gen5-preview-n3_a",
      "#Standard-series-primary-n3",
      "#Standard-series-primary-n3_a",
      "#Standard-series-preview-storage-n3",
      "#Standard-series-preview-storage-n3_a",
      "#sqldb-single-vcore-serverless-compute-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5_a"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "east-china3",
    "tableIDs": [
      "#T2-G-P",
      "#T2-G-PM",
      "#T2-B-P",
      "#T2-B-PM",
      "#SQL_General_Gen5_11_E3",
      "#MI_General_Purpose_Storage_14_E3",
      "#MI_2-1_Point_E3",
      "#MI_2_Long_E3",
      "#MI_Business_Critical_Gen5_16_E3",
      "#MI_Business_Critical_Storage_15_E3",
      "#MI_2-2_Point_E3",
      "#sqldb-managed-instance-general-purpose-gen5",
      "#Managed_Instance_NE3_1",
      "#Managed_Instance_NE3_2",
      "#sqldb-managed-instance-business-critical-gen5",
      "#Managed_Instance_NE3_3",
      "#Managed_Instance_NE3_4",
      "#All_NE3",
      "#All_NE1",
      "#All_NE2",
      "#Managed_Instance_Premium-series-2",
      "#sqldb-managed-instance-business-premium-serries-2",
      "#sqldb-managed-instance-business-premium-serries-2-yh"
    ]
  },
  {
    "os": "Managed Instance",
    "region": "north-china3",
    "tableIDs": [
      "#T2-G-P",
      "#T2-G-PM",
      "#T2-B-P",
      "#T2-B-PM",
      "#SQL_General_Gen5_11_E3",
      "#MI_General_Purpose_Storage_14_E3",
      "#MI_2-1_Point_E3",
      "#MI_2_Long_E3",
      "#MI_Business_Critical_Gen5_16_E3",
      "#MI_Business_Critical_Storage_15_E3",
      "#MI_2-2_Point_E3",
      "#sqldb-managed-instance-general-purpose-gen5",
      "#Managed_Instance_NE3_1",
      "#Managed_Instance_NE3_2",
      "#sqldb-managed-instance-business-critical-gen5",
      "#Managed_Instance_NE3_3",
      "#Managed_Instance_NE3_4",
      "#All_NE3",
      "#All_NE1",
      "#All_NE2",
      "#Managed_Instance_Premium-series-2",
      "#sqldb-managed-instance-business-premium-serries-2",
      "#sqldb-managed-instance-business-premium-serries-2-yh"
    ]
  },
  {
    "os": "Elastic Database",
    "region": "east-china3",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#Elastic_General_Purpose_Gen5_17_E3",
      "#Elastic_General_Fscv2_18_E3",
      "#Elastic_General_Storage_9_E3",
      "#Elastic_Business_Critical_Gen5_19_E3",
      "#Elastic_Business_M_20_E3",
      "#Elastic_Database_NE3_8_east3",
      "#Elastic_Database_NE3_5-east3",
      "#Elastic_Database_NE3_4_east3",
      "#Single_database_NE3_10_east3",
      "#Elastic_6_Elastic_Pools_Basic_E3",
      "#Elastic_7_Elastic_Pools_Standard_E3",
      "#Elastic_8_Elastic_Pools_Premium_E3",
      "#Elastic_Pool_Extra_Data_Storage_12_E3",
      "#Elastic_Database_NE3_1",
      "#Elastic_Database_NE3_2",
      "#Elastic_Database_NE3_3",
      "#sqldb-elastic-vcore-business-critical-gen4",
      "#sqldb-elastic-vcore-business-critical-gen5",
      "#Elastic_Database_NE3_5_east3",
      "#Elastic_Database_NE3_6",
      "#Elastic_Database_NE3_7",
      "#Elastic_Database_NE3_9",
      "#Elastic_Database_NE3_10",
      "#Elastic_Database_NE3_11",
      "#Elastic_Database_NE3_13",
      "#All_NE3",
      "#All_NE1",
      "#All_NE2",
      "#Managed_Instance_Premium-series-2",
      "#Elastic_Hyperscale_Gen5_24_E3",
      "#Standard-series-gen5-preview-n3-elastic",
      "#Standard-series-primary-n3-elastic",
      "#Standard-series-preview-storage-n3-elastic",
      "#Elastic_Database_NE3_4",
      "#Elastic_Database_NE3_8",
      "#Elastic_database_N3_2_a",
      "#Elastic_1-1_Long_N3_a",
      "#elastic_1-1_Point_N3",
      "#elastic_1-1_Long_N3",
      "#elastic_1-2_Point_N3",
      "#elastic_1-2_Long_N3",
      "#elastic_1-3_Point_N3",
      "#elastic_1-3_Long_N3",
      "#Elastic_Database_NE4",
      "#elastic_1-4_Point_N3",
      "#elastic_1-4_Long_N3"
    ]
  },
  {
    "os": "Elastic Database",
    "region": "north-china3",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#Elastic_General_Purpose_Gen5_17_E3",
      "#Elastic_General_Fscv2_18_E3",
      "#Elastic_General_Storage_9_E3",
      "#Elastic_Business_Critical_Gen5_19_E3",
      "#Elastic_Business_M_20_E3",
      "#Elastic_Database_NE3_8_east3",
      "#Elastic_Database_NE3_5-east3",
      "#Elastic_Database_NE3_4_east3",
      "#Single_database_NE3_10_east3",
      "#Elastic_6_Elastic_Pools_Basic_E3",
      "#Elastic_7_Elastic_Pools_Standard_E3",
      "#Elastic_8_Elastic_Pools_Premium_E3",
      "#Elastic_Pool_Extra_Data_Storage_12_E3",
      "#Elastic_Database_NE3_1",
      "#Elastic_Database_NE3_2",
      "#Elastic_Database_NE3_3",
      "#sqldb-elastic-vcore-business-critical-gen4",
      "#sqldb-elastic-vcore-business-critical-gen5",
      "#Elastic_Database_NE3_5_east3",
      "#Elastic_Database_NE3_6",
      "#Elastic_Database_NE3_7",
      "#Elastic_Database_NE3_9",
      "#Elastic_Database_NE3_10",
      "#Elastic_Database_NE3_11",
      "#Elastic_Database_NE3_13",
      "#All_NE3",
      "#All_NE1",
      "#All_NE2",
      "#Managed_Instance_Premium-series-2",
      "#Elastic_Hyperscale_Gen5_24_E3",
      "#Standard-series-gen5-preview-n3-elastic",
      "#Standard-series-primary-n3-elastic",
      "#Standard-series-preview-storage-n3-elastic",
      "#Elastic_Database_NE3_4",
      "#Elastic_Database_NE3_8",
      "#Elastic_database_E3_2_a",
      "#Elastic_1-1_Long_E3_a",
      "#elastic_1-1_Point_E3",
      "#elastic_1-1_Long_E3",
      "#elastic_1-2_Point_E3",
      "#elastic_1-2_Long_E3",
      "#elastic_1-3_Point_E3",
      "#elastic_1-3_Long_E3",
      "#Elastic_Database_NE4",
      "#elastic_1-4_Point_E3",
      "#elastic_1-4_Long_E3"
    ]
  },
  {
    "os": "Single database",
    "region": "east-china3",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#single_1-1_Point_E3",
      "#single_1-1_Long_E3",
      "#single_1-1_Long_N3_a",
      "#Single_General_Purpose_Gen5_17_E3",
      "#Single_General_Purpose_Gen5_17_E3_a",
      "#Single_General_Fscv2_18_E3",
      "#Single_General_Storage_9_E3",
      "#single_1-2_Point_N3",
      "#single_1-2_Long_N3",
      "#Single_General_IO_10_E3",
      "#Single_Business_Critical_Gen5_19_E3",
      "#Single_Business_M_20_E3",
      "#Single_Business_Critical_Storage_13_E3",
      "#Single_Hyperscale_Gen5_24_E3",
      "#Single_Hyperscale_storage_21_E3",
      "#Single_Hyperscale_IOs_22_E3",
      "#Single_5_basic_E3",
      "#Single_3_Dtu_Premium_E3",
      "#Single_database_NE3_1",
      "#Single_database_NE3_1_a",
      "#Single_database_NE3_2",
      "#Single_database_NE3_2_a",
      "#Single_database_NE3_2_1",
      "#Single_database_NE3_2_1_a",
      "#sqldb-single-vcore-general-purpose-gen4",
      "#sqldb-single-vcore-general-purpose-gen5_a",
      "#Single_database_NE3_3",
      "#Single_database_NE3_4",
      "#Single_database_NE3_5",
      "#Single_database_NE3_6",
      "#sqldb-single-vcore-business-critical-gen4",
      "#sqldb-single-vcore-business-critical-gen5",
      "#Single_database_NE3_7",
      "#Single_database_NE3_8",
      "#Single_database_NE3_9",
      "#Single_database_NE3_10",
      "#Single_database_NE3_11",
      "#Single_database_NE3_12",
      "#Single_database_NE3_13",
      "#Single_database_NE3_14",
      "#Single_database_NE3_16",
      "#Single_database_NE3_17",
      "#Single_database_NE3_18",
      "#All_NE3",
      "#All_NE1",
      "#All_NE2",
      "#sqldb-single-vcore-serverless-compute-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5_a",
      "#Elastic_database_N3_2_a",
      "#Single_Hyperscale_storage_21_N3",
      "#Single_Hyperscale_Gen5_24_N3",
      "#single_1-1_Point_N3_a",
      "#single_1-1_Point_N3",
      "#single_1-1_Long_N3",
      "#single_1-3_Point_N3",
      "#single_1-3_Long_N3",
      "#single_1-4_Point_N3",
      "#single_1-4_Long_N3"
    ]
  },
  {
    "os": "Single database",
    "region": "north-china3",
    "tableIDs": [
      "#sqldb-elastic-vcore-general-purpose-gen4",
      "#single_1-1_Point_E3",
      "#single_1-1_Point_E3_a",
      "#single_1-1_Long_E3",
      "#single_1-1_Long_E3_a",
      "#Single_General_Purpose_Gen5_17_E3",
      "#Single_General_Purpose_Gen5_17_E3_a",
      "#Single_General_Fscv2_18_E3",
      "#Single_General_Storage_9_E3",
      "#single_1-2_Point_E3",
      "#single_1-2_Long_E3",
      "#Single_General_IO_10_E3",
      "#Single_Business_Critical_Gen5_19_E3",
      "#Single_Business_M_20_E3",
      "#Single_Business_Critical_Storage_13_E3",
      "#single_1-3_Point_E3",
      "#single_1-3_Long_E3",
      "#Single_Hyperscale_Gen5_24_E3",
      "#Single_Hyperscale_storage_21_E3",
      "#single_1-4_Point_E3",
      "#Single_Hyperscale_IOs_22_E3",
      "#Single_5_basic_E3",
      "#Single_3_Dtu_Premium_E3",
      "#single_1-4_Long_E3",
      "#Single_database_NE3_1",
      "#Single_database_NE3_1_a",
      "#Single_database_NE3_2",
      "#Single_database_NE3_2_a",
      "#Single_database_NE3_2_1",
      "#Single_database_NE3_2_1_a",
      "#sqldb-single-vcore-general-purpose-gen4",
      "#sqldb-single-vcore-general-purpose-gen5_a",
      "#Single_database_NE3_3",
      "#Single_database_NE3_4",
      "#Single_database_NE3_5",
      "#Single_database_NE3_6",
      "#sqldb-single-vcore-business-critical-gen4",
      "#sqldb-single-vcore-business-critical-gen5",
      "#Single_database_NE3_7",
      "#Single_database_NE3_8",
      "#Single_database_NE3_9",
      "#Single_database_NE3_10",
      "#Single_database_NE3_11",
      "#Single_database_NE3_12",
      "#Single_database_NE3_13",
      "#Single_database_NE3_14",
      "#Single_database_NE3_16",
      "#Single_database_NE3_17",
      "#Single_database_NE3_18",
      "#All_NE3",
      "#All_NE1",
      "#All_NE2",
      "#sqldb-single-vcore-serverless-compute-gen5",
      "#sqldb-single-vcore-serverless-compute-gen5_a",
      "#Single_Hyperscale_storage_21_N3",
      "#single_1-4_Point_N3",
      "#Single_Hyperscale_Gen5_24_N3"
    ]
  },
  {
    "os": "MariaDB",
    "region": "north-china",
    "tableIDs": [
      "#mariadb-basic-compute-gen5",
      "#mariadb-general-purpose-compute-gen5",
      "#mariadb-memory-optimized-compute-gen5"
    ]
  },
  {
    "os": "MariaDB",
    "region": "east-china",
    "tableIDs": [
      "#mariadb-basic-compute-gen5",
      "#mariadb-general-purpose-compute-gen5",
      "#mariadb-memory-optimized-compute-gen5"
    ]
  },
  {
    "os": "Automation",
    "region": "east-china",
    "tableIDs": [
      "#Automation-control"
    ]
  },
  {
    "os": "Automation",
    "region": "north-china",
    "tableIDs": [
      "#Automation-control"
    ]
  },
  {
    "os": "Data Factory SSIS",
    "region": "north-china",
    "tableIDs": [
      "#data-factory-ssis-standard-a1v2-a8v2",
      "#data-factory-ssis-enterprise-a1v2-a8v2",
      "#data-factory-ssis-standard-d1v2-d4v2",
      "#data-factory-ssis-enterprise-d1v2-d4v2",
      "#data-factory-ssis-standard-e2v3-e64v3",
      "#data-factory-ssis-enterprise-e2v3-e64v3",
      "#data-factory-ssis-standard-d2v3-d64v3",
      "#data-factory-ssis-enterprise-d2v3-d64v3"
    ]
  },
  {
    "os": "Data Factory SSIS",
    "region": "east-china",
    "tableIDs": [
      "#data-factory-ssis-standard-a1v2-a8v2",
      "#data-factory-ssis-enterprise-a1v2-a8v2",
      "#data-factory-ssis-standard-d1v2-d4v2",
      "#data-factory-ssis-enterprise-d1v2-d4v2",
      "#data-factory-ssis-standard-e2v3-e64v3",
      "#data-factory-ssis-enterprise-e2v3-e64v3",
      "#data-factory-ssis-standard-d2v3-d64v3",
      "#data-factory-ssis-enterprise-d2v3-d64v3"
    ]
  },
  {
    "os": "Data Factory Data Pipeline",
    "region": "north-china2",
    "tableIDs": []
  },
  {
    "os": "Data Factory Data Pipeline",
    "region": "north-china",
    "tableIDs": [
      "#data-factory-data-pipeline-features",
      "#data-factory-data-pipeline-operation",
      "#Data-Flow-Execution-and-Debugging"
    ]
  },
  {
    "os": "Data Factory Data Pipeline",
    "region": "east-china",
    "tableIDs": [
      "#data-factory-data-pipeline-features",
      "#data-factory-data-pipeline-operation",
      "#Data-Flow-Execution-and-Debugging"
    ]
  },
  {
    "os": "Time Series Insights",
    "region": "east-china",
    "tableIDs": [
      "#time-series-insights-s1-s2",
      "#time-series-insights-analytics"
    ]
  },
  {
    "os": "Time Series Insights",
    "region": "north-china",
    "tableIDs": [
      "#time-series-insights-s1-s2",
      "#time-series-insights-analytics"
    ]
  },
  {
    "os": "App Windows",
    "region": "north-china",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-linux-v1",
      "#app-service-only-linux-v2",
      "#app-service-only-linux-v5",
      "#app-service-only-v2-windows-north3",
      "#app-service-only-v2-linux-north3"
    ]
  },
  {
    "os": "App Windows",
    "region": "north-china2",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-only-linux-v1",
      "#app-service-only-linux-v2",
      "#app-service-only-linux-v5",
      "#app-service-only-v2-linux-north3"
    ]
  },
  {
    "os": "App Windows",
    "region": "north-china3",
    "tableIDs": [
      "#app-service-only-linux-v1",
      "#app-service-only-linux-v2",
      "#app-service-only-linux-v5",
      "#app-service-only-v2-linux-north3"
    ]
  },
  {
    "os": "App Windows",
    "region": "east-china",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-linux-v1",
      "#app-service-only-linux-v2",
      "#app-service-only-linux-v5",
      "#app-service-only-v2-windows-north3",
      "#app-service-only-v2-linux-north3"
    ]
  },
  {
    "os": "App Windows",
    "region": "east-china2",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-only-linux-v1",
      "#app-service-only-linux-v2",
      "#app-service-only-linux-v5",
      "#app-service-only-v2-linux-north3"
    ]
  },
  {
    "os": "App Windows",
    "region": "east-china3",
    "tableIDs": [
      "#app-service-only-linux-v1",
      "#app-service-only-linux-v2",
      "#app-service-only-linux-v5",
      "#app-service-only-v2-linux-north3",
      "#app-service-only-window-funcomparison",
      "#app-service-only-window-freeAndshared"
    ]
  },
  {
    "os": "App Linux",
    "region": "east-china",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-windows-v1",
      "#app-service-only-windows-v2",
      "#app-service-only-windows-v3",
      "#app-service-only-windows-v4",
      "#app-service-only-windows-v5",
      "#app-service-only-windows-v6",
      "#app-service-only-windows-v7",
      "#app-service-only-windows-v8",
      "#app-service-only-linux-v3",
      "#app-service-only-linux-v4",
      "#app-service-only-linux-v6",
      "#app-service-only-linux-v6-new",
      "#app-service-only-linux-v7-new",
      "#app-service-only-v2-linux-north3",
      "#app-service-only-v2-windows-north3"
    ]
  },
  {
    "os": "App Linux",
    "region": "east-china2",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-windows-v1",
      "#app-service-only-windows-v2",
      "#app-service-only-windows-v3",
      "#app-service-only-windows-v4",
      "#app-service-only-windows-v5",
      "#app-service-only-windows-v6",
      "#app-service-only-windows-v7",
      "#app-service-only-windows-v8",
      "#app-service-only-v2-linux-north3",
      "#app-service-only-v2-windows-north3"
    ]
  },
  {
    "os": "App Linux",
    "region": "east-china3",
    "tableIDs": [
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-windows-v1",
      "#app-service-only-windows-v2",
      "#app-service-only-windows-v3",
      "#app-service-only-windows-v4",
      "#app-service-only-windows-v5",
      "#app-service-only-windows-v6",
      "#app-service-only-windows-v7",
      "#app-service-only-windows-v8",
      "#app-service-only-v2-windows-north3",
      "#app-service-only-linux-v4"
    ]
  },
  {
    "os": "App Linux",
    "region": "north-china",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-windows-v1",
      "#app-service-only-windows-v2",
      "#app-service-only-windows-v3",
      "#app-service-only-windows-v4",
      "#app-service-only-windows-v5",
      "#app-service-only-windows-v6",
      "#app-service-only-windows-v7",
      "#app-service-only-windows-v8",
      "#app-service-only-linux-v3",
      "#app-service-only-linux-v4",
      "#app-service-only-linux-v6",
      "#app-service-only-linux-v6-new",
      "#app-service-only-linux-v7-new",
      "#app-service-only-v2-linux-north3",
      "#app-service-only-v2-windows-north3"
    ]
  },
  {
    "os": "App Linux",
    "region": "north-china2",
    "tableIDs": [
      "#app-service-v3-windows-north3",
      "#app-service-v3-linux-north3",
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-windows-v1",
      "#app-service-only-windows-v2",
      "#app-service-only-windows-v3",
      "#app-service-only-windows-v4",
      "#app-service-only-windows-v5",
      "#app-service-only-windows-v6",
      "#app-service-only-windows-v7",
      "#app-service-only-windows-v8",
      "#app-service-only-v2-linux-north3",
      "#app-service-only-v2-windows-north3"
    ]
  },
  {
    "os": "App Linux",
    "region": "north-china3",
    "tableIDs": [
      "#app-service-service-plan-premium-v2",
      "#app-service-service-plan-isolated",
      "#app-service-only-windows-v1",
      "#app-service-only-windows-v2",
      "#app-service-only-windows-v3",
      "#app-service-only-windows-v4",
      "#app-service-only-windows-v5",
      "#app-service-only-windows-v6",
      "#app-service-only-windows-v7",
      "#app-service-only-windows-v8",
      "#app-service-only-v2-windows-north3"
    ]
  },
  {
    "os": "spring-cloud",
    "region": "east-china",
    "tableIDs": [
      "#spring-cloud-premium-region2",
      "#springcloud-standard-north3"
    ]
  },
  {
    "os": "spring-cloud",
    "region": "east-china2",
    "tableIDs": [
      "#qqqqq"
    ]
  },
  {
    "os": "spring-cloud",
    "region": "north-china2",
    "tableIDs": [
      "#qqqqq",
      "#springcloud-standard-north3"
    ]
  },
  {
    "os": "spring-cloud",
    "region": "north-china",
    "tableIDs": [
      "#spring-cloud-premium-region2",
      "#springcloud-standard-north3"
    ]
  },
  {
    "os": "spring-cloud",
    "region": "north-china3",
    "tableIDs": [
      
    ]
  },
  {
    "os": "Managed Disks",
    "region": "east-china",
    "tableIDs": [
      "#managed-disks-ssd-v2-no-3",
      "#managed-disks-ssd-v2-kz-no3",
      "#managed-disks-premium-region2",
      "#managed-disks-premium-region3",
      "#managed-disks-standard-ssd-region2",
      "#managed-disks-standard-ssd-region3",
      "#managed-disks-standard-hhd-region3",
      "#managed-disks-premium-ssd-snapshot-east3",
      "#managed-disks-standard-ssd-snapshot-east3",
      "#managed-disks-standard-hhd-snapshot-east3",
      "#managed-disks-ultra-disk-north-china3",
      "#managed-disks-ultra-disk-north-china3-config",
      "#managed-disks-premium-tufa",
      "#managed-disks-premium-tufa32",
      "#managed-disks-encryption-solution-n2",
      "#managed-disks-encryption-solution-n3",
      "#managed-disks-encryption-solution-n2e2n3"
    ]
  },
  {
    "os": "Managed Disks",
    "region": "east-china2",
    "tableIDs": [
      "#managed-disks-ssd-v2-no-3",
      "#managed-disks-ssd-v2-kz-no3",
      "#managed-disks-premium",
      "#managed-disks-premium-region3",
      "#managed-disks-standard-ssd",
      "#managed-disks-standard-ssd-region3",
      "#managed-disks-standard-hhd-region3",
      "#managed-disks-premium-ssd-snapshot-east3",
      "#managed-disks-standard-ssd-snapshot-east3",
      "#managed-disks-standard-hhd-snapshot-east3",
      "#managed-disks-ultra-disk-north-china3",
      "#managed-disks-ultra-disk-north-china3-config",
      "#managed-disks-premium-tufa",
      "#managed-disks-encryption-solution-n2",
      "#managed-disks-encryption-solution-n3"
    ]
  },
  {
    "os": "Managed Disks",
    "region": "north-china",
    "tableIDs": [
      "#managed-disks-ssd-v2-no-3",
      "#managed-disks-ssd-v2-kz-no3",
      "#managed-disks-premium-region2",
      "#managed-disks-premium-region3",
      "#managed-disks-standard-ssd-region2",
      "#managed-disks-standard-ssd-region3",
      "#managed-disks-standard-hhd-region3",
      "#managed-disks-premium-ssd-snapshot-east3",
      "#managed-disks-standard-ssd-snapshot-east3",
      "#managed-disks-standard-hhd-snapshot-east3",
      "#managed-disks-ultra-disk-north-china3",
      "#managed-disks-ultra-disk-north-china3-config",
      "#managed-disks-premium-tufa",
      "#managed-disks-premium-tufa32",
      "#managed-disks-encryption-solution-n2",
      "#managed-disks-encryption-solution-n3",
      "#managed-disks-encryption-solution-n2e2n3"
    ]
  },
  {
    "os": "Managed Disks",
    "region": "north-china2",
    "tableIDs": [
      "#managed-disks-ssd-v2-no-3",
      "#managed-disks-ssd-v2-kz-no3",
      "#managed-disks-premium",
      "#managed-disks-premium-region3",
      "#managed-disks-standard-ssd",
      "#managed-disks-standard-ssd-region3",
      "#managed-disks-standard-hhd-region3",
      "#managed-disks-premium-ssd-snapshot-east3",
      "#managed-disks-standard-ssd-snapshot-east3",
      "#managed-disks-standard-hhd-snapshot-east3",
      "#managed-disks-ultra-disk-north-china3",
      "#managed-disks-ultra-disk-north-china3-config",
      "#managed-disks-premium-tufa",
      "#managed-disks-encryption-solution-n3"
    ]
  },
  {
    "os": "Managed Disks",
    "region": "north-china3",
    "tableIDs": [

      "#managed-disks-premium",
      "#managed-disks-premium-region2",
      "#managed-disks-standard-ssd",
      "#managed-disks-standard-ssd-region3",
      "#managed-disks-standard-hhd-region3",
      "#managed-disks-premium-ssd-snapshot-east3",
      "#managed-disks-standard-ssd-snapshot-east3",
      "#managed-disks-standard-hhd-snapshot-east3",
      "#managed-disks-premium-tufa",
      "#managed-disks-encryption-solution-n2",
      "#managed-disks-encryption-solution-n2e2n3"
    ]
  },
  {
    "os": "Managed Disks",
    "region": "east-china3",
    "tableIDs": [

      "#managed-disks-premium",
      "#managed-disks-premium-region2",
      "#managed-disks-standard-ssd",
      "#managed-disks-standard-ssd-region3",
      "#managed-disks-standard-hhd-region3",
      "#managed-disks-premium-ssd-snapshot-east3",
      "#managed-disks-standard-ssd-snapshot-east3",
      "#managed-disks-standard-hhd-snapshot-east3",
      "#managed-disks-premium-tufa",
      "#managed-disks-encryption-solution-n2",
      "#managed-disks-encryption-solution-n3"
    ]
  },
  {
    "os": "Azure Dedicated Host",
    "region": "east-china",
    "tableIDs": [
      "#Azure-Dedicated-Host-1-east3",
      "#Azure-Dedicated-Host-1-north3",
      "#Azure-Dedicated-Host-1"
    ]
  },
  {
    "os": "Azure Dedicated Host",
    "region": "east-china2",
    "tableIDs": [
      "#Azure-Dedicated-Host-1-east3",
      "#Azure-Dedicated-Host-1-north3"
    ]
  },
  {
    "os": "Azure Dedicated Host",
    "region": "north-china",
    "tableIDs": [
      "#Azure-Dedicated-Host-1-east3",
      "#Azure-Dedicated-Host-1-north3",
      "#Azure-Dedicated-Host-1"
    ]
  },
  {
    "os": "Azure Dedicated Host",
    "region": "north-china2",
    "tableIDs": [
      "#Azure-Dedicated-Host-1-east3",
      "#Azure-Dedicated-Host-1-north3"
    ]
  },
  {
    "os": "Azure Dedicated Host",
    "region": "north-china3",
    "tableIDs": [
      "#Azure-Dedicated-Host-1-east3",
      "#Azure-Dedicated-Host-1"
    ]
  },
  {
    "os": "Azure Dedicated Host",
    "region": "east-china3",
    "tableIDs": [
      "#Azure-Dedicated-Host-1-north3",
      "#Azure-Dedicated-Host-1"
    ]
  },
  {
    "os": "data-explorer",
    "region": "north-china3",
    "tableIDs": [
      "#data-explorer-esv111"
    ]
  },
  {
    "os": "Azure Firewall Manager",
    "region": "north-china",
    "tableIDs": [
      "#firewall-manager"
    ]
  },
  {
    "os": "Azure Firewall Manager",
    "region": "east-china",
    "tableIDs": [
      "#firewall-manager"
    ]
  },
  {
    "os": "Storage Blobs",
    "region": "north-china3",
    "tableIDs": [
      "#Cool-and-Archive-early-deletion2",
      "#storage-blobs-blob-storage-change-feed-north",
      "#storage-blobs-gpv2-data-storage-east3",
      "#storage-blobs-gpv2-data-storage-region2",
      "#storage-blobs-gpv2-data-storage",
      "#storage-blobs-blob-storage-data-storage",
      "#storage-blobs-blob-storage-data-storage-region2",
      "#storage-blobs-blob-storage-data-storage-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove-east3",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy11",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy12",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy21",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy31",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-east3",
      "#storage-blobs-gpv2-data-transfer",
      "#storage-blobs-gpv2-change-feed",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1-east3",
      "#storage-blobs-blob-storage-operation-transfer",
      "#storage-blobs-blob-storage-operation-transfer-east3",
      "#storage-blobs-blob-storage-data-transfer",
      "#storage-blobs-blob-storage-data-transfer-east3",
      "#storage-blobs-blob-storage-change-feed-east3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption-east3",
      "#storage-blobs-gpv1-data-storage",
      "#storage-blobs-gpv1-data-storage-east3",
      "#storage-blobs-gv1-operation-transfer",
      "#storage-blobs-gv1-operation-transfer-east3",
      "#storage-blobs-v2-inventory-east2north2",
      "#storage-blobs-v2tier-inventory-east2",
      "#storage-blobs-v2tier-inventory-north2",
      "#storage-blobs-gpv2-hierarchical-namespace-data-storage-price-east3",
      "#Cool-and-Archive-early-deletion3-east3",
      "#storage_blobs-Data_storage_prices"
    ]
  },
  {
    "os": "Storage Blobs",
    "region": "north-china2",
    "tableIDs": [
      "#Cool-and-Archive-early-deletion3",
      "#Cool-and-Archive-early-deletion3-east3",
      "#storage_blobs-Data_storage_prices",
      "#storage_blobs-Data-Operations_and_data_transfer_prices",
      "#storage-blobs-blob-storage-change-feed-north",
      "#storage-blobs-gpv2-data-storage",
      "#storage-blobs-blob-storage-data-storage",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region",
      "#storage-blobs-gpv2-operations-and-data-other-prices-region",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy11",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy12",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy21",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy31",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove-east3",
      "#storage-blobs-gpv2-data-storage-east3",
      "#storage-blobs-gpv2-data-storage-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1-east3",
      "#storage-blobs-blob-storage-data-storage-region-east3",
      "#storage-blobs-blob-storage-data-storage-region-north3",
      "#storage-blobs-blob-storage-operation-transfer-east3",
      "#storage-blobs-blob-storage-operation-transfer-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption-east3",
      "#storage-blobs-gpv1-data-storage-east3",
      "#storage-blobs-gpv1-data-storage-north3",
      "#storage-blobs-gv1-operation-transfer-east3",
      "#storage-blobs-gv1-operation-transfer-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-north3",
      "#storage-blobs-blob-storage-data-transfer-east3",
      "#storage-blobs-blob-storage-data-transfer-north3",
      "#storage-blobs-blob-storage-change-feed-east3",
      "#storage-blobs-v2tier-inventory-east2",
      "#storage-blobs-gpv2-hierarchical-namespace-data-storage-price-east3",
      "#Cool-and-Archive-early-deletion3-north3",
      "#storage_blobs-Data_storage_prices_north3"
    ]
  },
  {
    "os": "Storage Blobs",
    "region": "north-china",
    "tableIDs": [
      "#Cool-and-Archive-early-deletion2",
      "#Cool-and-Archive-early-deletion3",
      "#Cool-and-Archive-early-deletion3-east3",
      "#storage_blobs-Data_storage_prices",
      "#storage_blobs-Data-Operations_and_data_transfer_prices",
      "#storage-blobs-blob-storage-change-feed",
      "#storage-blobs-gpv2-data-storage-region2",
      "#storage-blobs-blob-storage-data-storage-region2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3",
      "#storage-blobs-ssh-east2north2north3",
      "#storage-blobs-blob-storage-data-storage-region-index",
      "#storage-blobs-blob-storage-data-storage-region-indexs",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove-east3",
      "#storage-blobs-gpv2-data-storage-east3",
      "#storage-blobs-gpv2-data-storage-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1-east3",
      "#storage-blobs-blob-storage-data-storage-region-east3",
      "#storage-blobs-blob-storage-data-storage-region-north3",
      "#storage-blobs-blob-storage-operation-transfer-east3",
      "#storage-blobs-blob-storage-operation-transfer-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption-east3",
      "#storage-blobs-gpv1-data-storage-east3",
      "#storage-blobs-gpv1-data-storage-north3",
      "#storage-blobs-gv1-operation-transfer-east3",
      "#storage-blobs-gv1-operation-transfer-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-north3",
      "#storage-blobs-blob-storage-data-transfer-east3",
      "#storage-blobs-blob-storage-data-transfer-north3",
      "#storage-blobs-blob-storage-change-feed-east3",
      "#storage-blobs-v2-inventory-east2north2",
      "#storage-blobs-v2tier-inventory-east2",
      "#storage-blobs-v2tier-inventory-north2",
      "#storage-blobs-gpv2-hierarchical-namespace-data-storage-price-east3",
      "#Cool-and-Archive-early-deletion3-north3",
      "#storage_blobs-Data_storage_prices_north3"
    ]
  },
  {
    "os": "Storage Blobs",
    "region": "east-china3",
    "tableIDs": [
      "#Cool-and-Archive-early-deletion2",
      "#storage-blobs-blob-storage-change-feed-north",
      "#storage-blobs-gpv2-data-storage-east3",
      "#storage-blobs-gpv2-data-storage-region2",
      "#storage-blobs-gpv2-data-storage",
      "#storage-blobs-blob-storage-data-storage",
      "#storage-blobs-blob-storage-data-storage-region2",
      "#storage-blobs-blob-storage-data-storage-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove-east3",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy11",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy12",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy21",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy31",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-east3",
      "#storage-blobs-gpv2-data-transfer",
      "#storage-blobs-gpv2-change-feed",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1-east3",
      "#storage-blobs-blob-storage-operation-transfer",
      "#storage-blobs-blob-storage-operation-transfer-north3",
      "#storage-blobs-blob-storage-data-transfer",
      "#storage-blobs-blob-storage-data-transfer-east3",
      "#storage-blobs-blob-storage-change-feed-east3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption-east3",
      "#storage-blobs-gpv1-data-storage",
      "#storage-blobs-gpv1-data-storage-east3",
      "#storage-blobs-gv1-operation-transfer",
      "#storage-blobs-gv1-operation-transfer-east3",
      "#storage-blobs-v2-inventory-east2north2",
      "#storage-blobs-v2tier-inventory-east2",
      "#storage-blobs-v2tier-inventory-north2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-north3",
      "#Cool-and-Archive-early-deletion3-north3",
      "#storage_blobs-Data_storage_prices_north3"
    ]
  },
  {
    "os": "Storage Blobs",
    "region": "east-china2",
    "tableIDs": [
      "#Cool-and-Archive-early-deletion3",
      "#Cool-and-Archive-early-deletion3-east3",
      "#storage_blobs-Data_storage_prices",
      "#storage_blobs-Data-Operations_and_data_transfer_prices",
      "#storage-blobs-blob-storage-change-feed-north",
      "#storage-blobs-gpv2-data-storage",
      "#storage-blobs-blob-storage-data-storage",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region",
      "#storage-blobs-gpv2-operations-and-data-other-prices-region",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy11",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy12",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy21",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy31",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove-east3",
      "#storage-blobs-gpv2-data-storage-east3",
      "#storage-blobs-gpv2-data-storage-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1-east3",
      "#storage-blobs-blob-storage-data-storage-region-east3",
      "#storage-blobs-blob-storage-data-storage-region-north3",
      "#storage-blobs-blob-storage-operation-transfer-east3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption-east3",
      "#storage-blobs-gpv1-data-storage-east3",
      "#storage-blobs-gpv1-data-storage-north3",
      "#storage-blobs-gv1-operation-transfer-east3",
      "#storage-blobs-gv1-operation-transfer-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-north3",
      "#storage-blobs-blob-storage-data-transfer-east3",
      "#storage-blobs-blob-storage-data-transfer-north3",
      "#storage-blobs-blob-storage-change-feed-east3",
      "#storage-blobs-v2tier-inventory-north2",
      "#storage-blobs-gpv2-hierarchical-namespace-data-storage-price-east3",
      "#Cool-and-Archive-early-deletion3-north3",
      "#storage_blobs-Data_storage_prices_north3",
      "#storage-blobs-blob-storage-operation-transfer-east3",
      "#storage-blobs-blob-storage-operation-transfer-north3"
    ]
  },
  {
    "os": "Storage Blobs",
    "region": "east-china",
    "tableIDs": [
      "#Cool-and-Archive-early-deletion2",
      "#Cool-and-Archive-early-deletion3",
      "#Cool-and-Archive-early-deletion3-east3",
      "#storage_blobs-Data_storage_prices",
      "#storage_blobs-Data-Operations_and_data_transfer_prices",
      "#storage-blobs-blob-storage-change-feed",
      "#storage-blobs-gpv2-data-storage-region2",
      "#storage-blobs-blob-storage-data-storage-region2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3",
      "#storage-blobs-blob-storage-data-storage-region-index",
      "#storage-blobs-blob-storage-data-storage-region-indexs",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3",
      "#storage-blobs-gpv2-operations-and-data-other-prices-regions-before-remove-east3",
      "#storage-blobs-ssh-east2north2north3",
      "#storage-blobs-gpv2-data-storage-east3",
      "#storage-blobs-gpv2-data-storage-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-region-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption1-1-east3",
      "#storage-blobs-blob-storage-data-storage-region-east3",
      "#storage-blobs-blob-storage-data-storage-region-north3",
      "#storage-blobs-blob-storage-operation-transfer-east3",
      "#storage-blobs-blob-storage-operation-transfer-north3",
      "#storage-blobs-blob-storage-data-storage-region-indexs-Encryption-east3",
      "#storage-blobs-gpv1-data-storage-east3",
      "#storage-blobs-gpv1-data-storage-north3",
      "#storage-blobs-gv1-operation-transfer-east3",
      "#storage-blobs-gv1-operation-transfer-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy1-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy2-north3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-east3",
      "#storage-blobs-gpv2-operations-and-data-transfer-prices-hierarchy3-north3",
      "#storage-blobs-blob-storage-data-transfer-east3",
      "#storage-blobs-blob-storage-data-transfer-north3",
      "#storage-blobs-blob-storage-change-feed-east3",
      "#storage-blobs-v2-inventory-east2north2",
      "#storage-blobs-v2tier-inventory-east2",
      "#storage-blobs-v2tier-inventory-north2",
      "#storage-blobs-gpv2-hierarchical-namespace-data-storage-price-east3",
      "#Cool-and-Archive-early-deletion3-north3",
      "#storage_blobs-Data_storage_prices_north3"
    ]
  },
  {
    "os": "Azure Synapse Analytics",
    "region": "east-china",
    "tableIDs": [
      "#Azure_Synapse_Analytics1-1",
      "#Azure_Synapse_Analytics2-1",
      "#Azure_Synapse_Analytics3-1",
      "#Azure_Synapse_Analytics0-1",
      "#Azure_Synapse_Analytics0-2",
      "#Azure_Synapse_Analytics0-3",
      "#Azure_Synapse_Analytics3-2",
      "#Azure_Synapse_Analytics3-2-1",
      "#Azure_Synapse_Analytics_new_1-1",
      "#Azure_Synapse_Analytics_new_0_1-1",
      "#Azure_Synapse_Analytics_new_1-1ex",
      "#Azure_Synapse_Analytics3-1ex",
      "#Azure_Synapse_Analytics3-1exx",
      "#Azure_Synapse_Analytics_new_1-1ex1",
      "#Azure_Synapse_Analytics3-1ex1",
      "#Azure_Synapse_Analytics3-1exx1",
      "#Azure_Synapse_Analytics3-online",
      "#Azure_Synapse_Analytics3-online-1",
      "#data-factory-ssis-standard-a1v2-a8v2",
      "#data-factory-ssis-enterprise-a1v2-a8v2",
      "#data-factory-ssis-standard-d1v2-d4v2",
      "#data-factory-ssis-enterprise-d1v2-d4v2",
      "#data-factory-ssis-standard-e2v3-e64v3",
      "#data-factory-ssis-enterprise-e2v3-e64v3",
      "#data-factory-ssis-standard-d2v3-d64v3",
      "#data-factory-ssis-enterprise-d2v3-d64v3"
    ]
  },
  {
    "os": "Azure Synapse Analytics",
    "region": "north-china",
    "tableIDs": [
      "#Azure_Synapse_Analytics1-1",
      "#Azure_Synapse_Analytics2-1",
      "#Azure_Synapse_Analytics3-1",
      "#Azure_Synapse_Analytics0-1",
      "#Azure_Synapse_Analytics0-2",
      "#Azure_Synapse_Analytics0-3",
      "#Azure_Synapse_Analytics3-2",
      "#Azure_Synapse_Analytics3-2-1",
      "#Azure_Synapse_Analytics_new_1-1",
      "#Azure_Synapse_Analytics_new_0_1-1",
      "#Azure_Synapse_Analytics_new_1-1ex",
      "#Azure_Synapse_Analytics3-1ex",
      "#Azure_Synapse_Analytics3-1exx",
      "#Azure_Synapse_Analytics_new_1-1ex1",
      "#Azure_Synapse_Analytics3-1ex1",
      "#Azure_Synapse_Analytics3-1exx1",
      "#Azure_Synapse_Analytics3-online",
      "#Azure_Synapse_Analytics3-online-1",
      "#data-factory-ssis-standard-a1v2-a8v2",
      "#data-factory-ssis-enterprise-a1v2-a8v2",
      "#data-factory-ssis-standard-d1v2-d4v2",
      "#data-factory-ssis-enterprise-d1v2-d4v2",
      "#data-factory-ssis-standard-e2v3-e64v3",
      "#data-factory-ssis-enterprise-e2v3-e64v3",
      "#data-factory-ssis-standard-d2v3-d64v3",
      "#data-factory-ssis-enterprise-d2v3-d64v3"
    ]
  },
  {
    "os": "Azure Synapse Analytics",
    "region": "east-china2",
    "tableIDs": [
      "#Azure_Synapse_Analytics3-2",
      "#Azure_Synapse_Analytics3-2-1",
      "#Azure_Synapse_Analytics3-online2",
      "#Azure_Synapse_Analytics3-online2-1"
    ]
  },
  {
    "os": "Azure Synapse Analytics",
    "region": "north-china2",
    "tableIDs": [
      "#Azure_Synapse_Analytics3-online2",
      "#Azure_Synapse_Analytics3-online2-1"
    ]
  },
  {
    "os": "Azure Synapse Analytics",
    "region": "north-china3",
    "tableIDs": [
      "#Azure_Synapse_Analytics3-online",
      "#Azure_Synapse_Analytics3-online-1",
      "#Azure_Synapse_Analytics0-2",
      "#Azure_Synapse_Analytics3-1exx",
      "#Azure_Synapse_Analytics0-3",
      "#Azure_Synapse_Analytics0-3-a",
      "#Azure_Synapse_Analytics_new_1-1ex",
      "#Azure_Synapse_Analytics_new_1-1ex1"
    ]
  },
  {
    "os": "Database Migration",
    "region": "east-china",
    "tableIDs": [
      "#database-migration-standard-compute",
      "#database-migration-premium-compute"
    ]
  },
  {
    "os": "Database Migration",
    "region": "north-china2",
    "tableIDs": [
      "#database-migration-standard-compute",
      "#database-migration-premium-compute"
    ]
  },
  {
    "os": "Database Migration",
    "region": "north-china",
    "tableIDs": [
      "#database-migration-standard-compute",
      "#database-migration-premium-compute"
    ]
  },
  {
    "os": "Azure Functions",
    "region": "north-china2",
    "tableIDs": [
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-resource-consumption",
      "#azure-functions-table-total-plan-functions-north3"
    ]
  },
  {
    "os": "Azure Functions",
    "region": "north-china3",
    "tableIDs": [
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-resource-consumption",
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-plan-functions",
      "#azure-functions-table-resource-consumption"
    ]
  },
  {
    "os": "Azure Functions",
    "region": "east-china3",
    "tableIDs": [
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-resource-consumption",
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-plan-functions",
      "#azure-functions-table-resource-consumption"
    ]
  },
  {
    "os": "Azure Functions",
    "region": "east-china",
    "tableIDs": [
      "#azure-functions-table-rate-per-second",
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-resource-consumption",
      "#azure-functions-table-total-plan-functions",
      "#azure-functions-table-total-plan-functions-north3"
    ]
  },
  {
    "os": "Azure Functions",
    "region": "north-china",
    "tableIDs": [
      "#azure-functions-table-total-plan-functions",
      "#azure-functions-table-total-plan-functions-north3"
    ]
  },
  {
    "os": "Azure Functions",
    "region": "east-china2",
    "tableIDs": [
      "#azure-functions-table-execute-times",
      "#azure-functions-table-total-resource-consumption",
      "#azure-functions-table-total-plan-functions-north3"
    ]
  },
  {
    "os": "Application Gateway",
    "region": "east-china",
    "tableIDs": [
      "#application-gateway-table-standard-v2",
      "#application-gateway-2"
    ]
  },
  {
    "os": "Application Gateway",
    "region": "east-china2",
    "tableIDs": [
      "#application-gateway-2"
    ]
  },
  {
    "os": "Application Gateway",
    "region": "north-china",
    "tableIDs": [
      "#application-gateway-table-standard-v2",
      "#application-gateway-2"
    ]
  },
  {
    "os": "Application Gateway",
    "region": "north-china2",
    "tableIDs": [
      "#application-gateway-2"
    ]
  },
  {
    "os": "Application Gateway",
    "region": "north-china3",
    "tableIDs": [
      "#application-gateway-1"
    ]
  },
  {
    "os": "Application Gateway",
    "region": "east-china3",
    "tableIDs": [
      "#application-gateway-1"
    ]
  },
  {
    "os": "Cognitive Services",
    "region": "east-china2",
    "tableIDs": [
      "#cognitive-services-table-speech-services",
      "#cognitive-services-table-speech-services-neural",
      "#cognitive-services-table-speech-services-north"
    ]
  },
  {
    "os": "Cognitive Services",
    "region": "north-china3",
    "tableIDs": [
      "#cognitive-services-table-speech-services",
      "#cognitive-services-table-speech-services-neural",
      "#cognitive-services-table-speech-services-north",
     "#cognitive-services-table-speech-commitment-tiers-north2east2",
      "#cognitive-services1",
      "#cognitive-services-table-training",
      "#cognitive-services5"
    ]
  },
  {
    "os": "Cognitive Services",
    "region": "east-china",
    "tableIDs": [
      "#cognitive-services-table-speech-services",
      "#cognitive-services-table-speech-services-neural",
      "#cognitive-services-table-speech-services-east2",
      "#cognitive-services-table-speech-servicesv1",
      "#cognitive-services1",
      "#cognitive-services2",
      "#cognitive-services3",
      "#cognitive-services4",
      "#cognitive-services5",
      "#cognitive-services-table-admitlevel-nor2east2",
      "#cognitive-services-table-speech-commitment-tiers-north2east2",
      "#cognitive-services-LanguageService-nor2east2",
      "#cognitive-services-table-speech-services-tier-nor2east2",
      "#cognitive-services-north3"
    ]
  },
  {
    "os": "Cognitive Services",
    "region": "north-china2",
    "tableIDs": [
      "#cognitive-services-table-speech-services",
      "#cognitive-services-table-speech-servicesv1",
      "#cognitive-services-table-speech-services-east2",
      "#cognitive-services-table-speech-services-north",
      "#cognitive-services1",
      "#cognitive-services3",
      "#cognitive-services4",
      "#cognitive-services5",
      "#cognitive-services2",
      "#cognitive-services-north3"
    ]
  },
  {
    "os": "Cognitive Services",
    "region": "north-china",
    "tableIDs": [
      "#cognitive-services-table-speech-services-neural",
      "#cognitive-services-table-speech-services-east2",
      "#cognitive-services-table-speech-servicesv1",
      "#cognitive-services1",
      "#cognitive-services3",
      "#cognitive-services4",
      "#cognitive-services5",
      "#cognitive-services-table-admitlevel-nor2east2",
      "#cognitive-services-table-speech-commitment-tiers-north2east2",
      "#cognitive-services-LanguageService-nor2east2",
      "#cognitive-services-table-speech-services-tier-nor2east2",
      "#cognitive-services-north3"
    ]
  },

  {
    "os": "Power BI Embedded",
    "region": "north-china3",
    "tableIDs": [
      "#power-bi-embedded-table-a7a8"
    ]
  },
  {
    "os": "Power BI Embedded",
    "region": "east-china2",
    "tableIDs": [
      "#power-bi-embedded-table-a7a8"
    ]
  },
  {
    "os": "Power BI Embedded",
    "region": "north-china2",
    "tableIDs": [
      "#power-bi-embedded-table-a7a8"
    ]
  },
  {
    "os": "Power BI Embedded",
    "region": "east-china",
    "tableIDs": [
      "#power-bi-embedded-table-hide-a7a8"
    ]
  },
  {
    "os": "Power BI Embedded",
    "region": "north-china",
    "tableIDs": [
      "#power-bi-embedded-table-hide-a7a8"
    ]
  },

  {
    "os": "Network Watcher",
    "region": "north-china3",
    "tableIDs": [
      "#network-watcher-region",
      "#network-watcher-region2"
    ]
  },
  {
    "os": "Network Watcher",
    "region": "north-china",
    "tableIDs": [
      "#network-watcher-region3",
      "#network-watcher-region2"
    ]
  },
  {
    "os": "Network Watcher",
    "region": "north-china2",
    "tableIDs": [
      "#network-watcher-region2",
      "#network-watcher-region"
    ]
  },
  {
    "os": "Network Watcher",
    "region": "east-china",
    "tableIDs": [
      "#network-watcher-region3",
      "#network-watcher-region2"
    ]
  },
  {
    "os": "Network Watcher",
    "region": "east-china2",
    "tableIDs": [
      "#network-watcher-region",
      "#network-watcher-region2"
    ]
  },
  {
    "os": "Active Directory",
    "region": "east-china",
    "tableIDs": [
      "#active-directory-standard-tier-region"
    ]
  },
  {
    "os": "Active Directory",
    "region": "north-china",
    "tableIDs": [
      "#active-directory-standard-tier-region"
    ]
  },
  {
    "os": "Azure Stack HCI",
    "region": "east-china",
    "tableIDs": [
      "#Azure-Stack-HCI-region",
      "#Azure-Stack-HCI-region-1"
    ]
  },
  {
    "os": "Azure Stack HCI",
    "region": "north-china",
    "tableIDs": [
      "#Azure-Stack-HCI-region",
      "#Azure-Stack-HCI-region-1"
    ]
  },
  {
    "os": "Azure Stack HCI",
    "region": "north-china2",
    "tableIDs": [
      "#Azure-Stack-HCI-region"
    ]
  },
  {
    "os": "App Configuration",
    "region": "east-china",
    "tableIDs": [
      "#pricing-information-standard-app-configuration"
    ]
  },
  {
    "os": "App Configuration",
    "region": "east-china2",
    "tableIDs": []
  },
  {
    "os": "App Configuration",
    "region": "north-china",
    "tableIDs": [
      "#pricing-information-standard-app-configuration"
    ]
  },
  {
    "os": "App Configuration",
    "region": "north-china2",
    "tableIDs": []
  },
  {
    "os": "App Configuration",
    "region": "north-china3",
    "tableIDs": []
  },
  {
    "os": "Azure Bastion",
    "region": "east-china2",
    "tableIDs": []
  },
  {
    "os": "Azure Bastion",
    "region": "east-china",
    "tableIDs": [
      "#azure-bastion-region2",
      "#azure-bastion-region1"
    ]
  },
  {
    "os": "Azure Bastion",
    "region": "north-china",
    "tableIDs": [
      "#azure-bastion-region2",
      "#azure-bastion-region1"
    ]
  },
  {
    "os": "Azure AI Search",
    "region": "east-china",
    "tableIDs": [
      "#Azure-Cognitive-Search1",
      "#Azure-Cognitive-Search2",
      "#Azure-Cognitive-Search3"
    ]
  },
  {
    "os": "Azure AI Search",
    "region": "north-china",
    "tableIDs": [
      "#Azure-Cognitive-Search1",
      "#Azure-Cognitive-Search2",
      "#Azure-Cognitive-Search3"
    ]
  },
  {
    "os": "Azure AI Search",
    "region": "north-china2",
    "tableIDs": [
      "#Azure-Cognitive-Search1"
    ]
  },
  {
    "os": "Azure AI Search",
    "region": "east-china2",
    "tableIDs": [
      "#Azure-Cognitive-Search1"
    ]
  },
  {
    "os": "Azure AI Search",
    "region": "north-china3",
    "tableIDs": [
      "#Azure-Cognitive-Search3"
    ]
  },
  {
    "os": "Azure AI Search",
    "region": "north-china",
    "tableIDs": [
      "#Azure-Cognitive-Search1",
      "#Azure-Cognitive-Search2"
    ]
  },
  {
    "os": "Media Services",
    "region": "north-china",
    "tableIDs": [
      "#media-service-regin",
      "#Live-Video-north3"
    ]
  },
  {
    "os": "Media Services",
    "region": "east-china",
    "tableIDs": [
      "#media-service-regin",
      "#Live-Video-north3",
      "#Live-Video-1-north3"
    ]
  },
  {
    "os": "Media Services",
    "region": "east-china2",
    "tableIDs": [
      "#Live-Video-north3"
    ]
  },
  {
    "os": "Media Services",
    "region": "north-china2",
    "tableIDs": [
      "#Live-Video-north3"
    ]
  },
  {
    "os": "Media Services",
    "region": "north-china3",
    "tableIDs": [
      "#Live-Video-1-north3",
      "Live-Video-2-north3",
      "#media-service-regin",
      "#media-service-face",
      "#media-service-analyze"
    ]
  },
  {
    "os": "Analysis Services",
    "region": "east-china2",
    "tableIDs": [
      "#analysis-services-standard-tier-region",
      "#analysis-services-standard-tier-s0",
      "#analysis-services-standard-tier-s1",
      "#analysis-services-standard-tier-new-s1",
      "#analysis-services-standard-tier-new-s2"
    ]
  },
  {
    "os": "Analysis Services",
    "region": "east-china",
    "tableIDs": [
      "#analysis-services-standard-tier-region2",
      "#analysis-services-standard-tier-region3",
      "#analysis-services-standard-tier-new-s1",
      "#analysis-services-standard-tier-new-s2"
    ]
  },
  {
    "os": "Analysis Services",
    "region": "north-china2",
    "tableIDs": [
      "#analysis-services-standard-tier-region2",
      "#analysis-services-standard-tier-region3",
      "#analysis-services-standard-tier-s0",
      "#analysis-services-standard-tier-s1",
      "#analysis-services-standard-tier-new-s1",
      "#analysis-services-standard-tier-new-s2"
    ]
  },
  {
    "os": "Analysis Services",
    "region": "north-china",
    "tableIDs": [
      "#analysis-services-standard-tier-s0",
      "#analysis-services-standard-tier-s1",
      "#analysis-services-standard-tier-region3",
      "#analysis-services-standard-tier-region2"
    ]
  },
  {
    "os": "Backup",
    "region": "east-china3",
    "tableIDs": [
      "#backup-table-vm-backup-space-eastandnorth",
      "#backup-table-sap-hana-space-eastandnorth",
      "#backup-table-sql-server-space-eastnorth",
      "#backup-table-vm-backup-2",
      "#backup-table-vm-backup",
      "#backup-table-vm-backup-space-east3",
      "#backup-table-vm-backup-space",
      "#backup-table-sql-server-east3",
      "#backup-table-sql-server-space-east3",
      "#backup-table-sql-server-space",
      "#backup-table-sap-hana-space",
      "#backup-table-sap-hana-east3",
      "#backup-table-vm-backup-1-north3",
      "#backup-table-sap-hana-space-east3"
    ]
  },
  {
    "os": "Backup",
    "region": "east-china2",
    "tableIDs": [
      "#backup-table-vm-backup-space-eastandnorth",
      "#backup-table-sap-hana-space-eastandnorth",
      "#backup-table-sql-server-space-eastnorth",
      "#backup-table-vm-backup-space-east3",
      "#backup-table-vm-backup-space-north3",
      "#backup-table-sql-server-east3",
      "#backup-table-vm-backup-north3",
      "#backup-table-sql-server-space-east3",
      "#backup-table-vm-backup-north3-1",
      "#backup-table-sap-hana-east3",
      "#backup-table-sap-hana-space-north3",
      "#backup-table-sql-server-space-north3",
      "#backup-table-sap-hana-space-east3",
      "#backup-table-vm-backup-1-north3"
    ]
  },
  {
    "os": "Backup",
    "region": "east-china",
    "tableIDs": [
      "#backup-table-sql-server-space",
      "#backup-table-sap-hana-space",
      "#backup-table-vm-backup-space",
      "#backup-table-vm-backup-2",
      "#backup-table-vm-backup-space-east3",
      "#backup-table-vm-backup-space-north3",
      "#backup-table-sql-server-east3",
      "#backup-table-vm-backup-north3-1",
      "#backup-table-sql-server-space-east3",
      "#backup-table-vm-backup-north3",
      "#backup-table-sap-hana-east3",
      "#backup-table-sap-hana-space-north3",
      "#backup-table-sql-server-space-north3",
      "#backup-table-sap-hana-space-east3",
      "#backup-table-vm-backup-1-north3",
      "#backup-table-azure-file-storage-east2andnorth2andnorth3"
    ]
  },
  {
    "os": "Backup",
    "region": "north-china3",
    "tableIDs": [
      "#backup-table-vm-backup-space-eastandnorth",
      "#backup-table-sap-hana-space-eastandnorth",
      "#backup-table-sql-server-space-eastnorth",
      "#backup-table-vm-backup-2",
      "#backup-table-vm-backup",
      "#backup-table-vm-backup-space-east3",
      "#backup-table-vm-backup-space",
      "#backup-table-sql-server-east3",
      "#backup-table-sql-server-space-east3",
      "#backup-table-sql-server-space",
      "#backup-table-sap-hana-space",
      "#backup-table-sap-hana-east3",
      "#backup-table-vm-backup-1-north3",
      "#backup-table-sap-hana-space-east3"
    ]
  },
  {
    "os": "Backup",
    "region": "north-china2",
    "tableIDs": [
      "#backup-table-vm-backup-space-eastandnorth",
      "#backup-table-sap-hana-space-eastandnorth",
      "#backup-table-sql-server-space-eastnorth",
      "#backup-table-vm-backup-space-east3",
      "#backup-table-vm-backup-north3-1",
      "#backup-table-vm-backup-space-north3",
      "#backup-table-sql-server-east3",
      "#backup-table-sql-server-space-east3",
      "#backup-table-sap-hana-east3",
      "#backup-table-vm-backup-north3",
      "#backup-table-sap-hana-space-north3",
      "#backup-table-sql-server-space-north3",
      "#backup-table-sap-hana-space-east3",
      "#backup-table-vm-backup-1-north3"
    ]
  },
  {
    "os": "Backup",
    "region": "north-china",
    "tableIDs": [
      "#backup-table-vm-backup-space",
      "#backup-table-sql-server-space",
      "#backup-table-sap-hana-space",
      "#backup-table-vm-backup-2",
      "#backup-table-vm-backup-north3-1",
      "#backup-table-vm-backup-space-east3",
      "#backup-table-vm-backup-space-north3",
      "#backup-table-sql-server-east3",
      "#backup-table-sql-server-space-east3",
      "#backup-table-vm-backup-north3",
      "#backup-table-sap-hana-east3",
      "#backup-table-sql-server-space-north3",
      "#backup-table-sap-hana-space-north3",
      "#backup-table-sap-hana-space-east3",
      "#backup-table-vm-backup-1-north3",
      "#backup-table-azure-file-storage-east2andnorth2andnorth3"    
    ]
  },
  {
    "os": "Signalr Service",
    "region": "north-china2",
    "tableIDs": [
      "#signalr-service-table-free-standard",
      "#signalr-service-table-others-information"
    ]
  },
  {
    "os": "Signalr Service",
    "region": "north-china",
    "tableIDs": [
      "#signalr-service-table-free-standard",
      "#signalr-service-table-others-information"
    ]
  },
  {
    "os": "Signalr Service",
    "region": "east-china",
    "tableIDs": [
      "#signalr-service-table-free-standard",
      "#signalr-service-table-others-information"
    ]
  },
  {
    "os": "Container Instances - Linux",
    "region": "east-china",
    "tableIDs": [
      "#container-instances-linux-table-north3east2",
      "#container-instances-linux-table-north2"
    ]
  },
  {
    "os": "Container Instances - Linux",
    "region": "north-china",
    "tableIDs": [
      "#container-instances-linux-table-north3east2",
      "#container-instances-linux-table-north2"
    ]
  },
  {
    "os": "Container Instances - Linux",
    "region": "north-china2",
    "tableIDs": [
      "#container-instances-linux-table-north3east2"
    ]
  },
  {
    "os": "Container Instances - Linux",
    "region": "east-china2",
    "tableIDs": [
      "#container-instances-linux-table-north2"
    ]
  },
  {
    "os": "Container Instances - Linux",
    "region": "north-china3",
    "tableIDs": [
      "#container-instances-linux-table-north2"
    ]
  },
  {
    "os": "Container Instances - Windows",
    "region": "east-china",
    "tableIDs": [
      "#container-instances-windows-table"
    ]
  },
  {
    "os": "Container Instances - Windows",
    "region": "north-china",
    "tableIDs": [
      "#container-instances-windows-table"
    ]
  },
  {
    "os": "Container Instances - Windows",
    "region": "north-china2",
    "tableIDs": [
      "#container-instances-windows-table"
    ]
  },
  {
    "os": "Logic Apps",
    "region": "north-china",
    "tableIDs": [
      "#logic-apps-integration-service-environment-table",
      "#logic-apps-integration-service-environment-table1",
      "#logic-apps-integration-service-environment-north3-china-table"
    ]
  },
  {
    "os": "Logic Apps",
    "region": "east-china",
    "tableIDs": [
      "#logic-apps-integration-service-environment-table",
      "#logic-apps-integration-service-environment-table1",
      "#logic-apps-integration-service-environment-north3-china-table"
    ]
  },
  {
    "os": "Logic Apps",
    "region": "east-china2",
    "tableIDs": [
      "#logic-apps-integration-service-environment-north3-china-table"
    ]
  },
  {
    "os": "Logic Apps",
    "region": "north-china3",
    "tableIDs": [
      "#logic-apps-integration-service-environment-table1",
      "#logic-apps-integration-service-environment-table"
    ]
  },
  {
    "os": "Logic Apps",
    "region": "north-china2",
    "tableIDs": [
      "#logic-apps-integration-service-environment-north3-china-table"
    ]
  },
  {
    "os": "Search",
    "region": "east-china",
    "tableIDs": [
      "#storage-files-date-search-price-table"
    ]
  },
  {
    "os": "Search",
    "region": "north-china",
    "tableIDs": [
      "#storage-files-date-search-price-table"
    ]
  },
  {
    "os": "Search",
    "region": "east-china2",
    "tableIDs": [
      "#storage-files-date-search-price-table2"
    ]
  },
  {
    "os": "Search",
    "region": "north-china2",
    "tableIDs": [
      "#storage-files-date-search-price-table"
    ]
  },
  {
    "os": "Storage Files",
    "region": "east-china",
    "tableIDs": [
      "#storage-files-date-storage-price-table2",
      "#storage-files-date-storage-price-table3",
      "#storage-files-date-storage-price-table4",
      "#storage-files-date-storage-price-table-new1first",
      "#storage-files-date-storage-price-table-new1",
      "#storage-files-date-storage-price-table-new2",
      "#storage-files-date-storage-price-table-new3",
      "#storage-files-date-storage-price-table-new4s",
      "#storage-files-date-storage-price-table-new1-north3",
      "#storage-files-date-storage-price-table-new1-east3",
      "#storage-files-date-storage-price-table-new2-north3",
      "#storage-files-date-storage-price-table-new2-east3",
      "#storage-files-data-storage-transactions-transfer-north3",
      "#storage-files-data-storage-transactions-transfer-east3",
      "#storage-files-date-storage-price-GZRS-table-china-north3",
      "#storage-files-date-storage-price-ZRS-table-china-north3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"
    ]
  },
  {
    "os": "Storage Files",
    "region": "north-china",
    "tableIDs": [
      "#storage-files-date-storage-price-table2",
      "#storage-files-date-storage-price-table3",
      "#storage-files-date-storage-price-table4",
      "#storage-files-date-storage-price-table-new1first",
      "#storage-files-date-storage-price-table-new1",
      "#storage-files-date-storage-price-table-new2",
      "#storage-files-date-storage-price-table-new3",
      "#storage-files-date-storage-price-table-new4s",
      "#storage-files-date-storage-price-table-new1-north3",
      "#storage-files-date-storage-price-table-new1-east3",
      "#storage-files-date-storage-price-table-new2-north3",
      "#storage-files-date-storage-price-table-new2-east3",
      "#storage-files-data-storage-transactions-transfer-north3",
      "#storage-files-data-storage-transactions-transfer-east3",
      "#storage-files-date-storage-price-GZRS-table-china-north3",
      "#storage-files-date-storage-price-ZRS-table-china-north3",    
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"
    ]
  },
  {
    "os": "Storage Files",
    "region": "east-china2",
    "tableIDs": [
      "#storage-files-date-storage-price-table",
      "#storage-files-date-storage-price-table-new1s",
      "#storage-files-date-storage-price-table-new2s",
      "#storage-files-date-storage-price-table-new3s",
      "#storage-files-date-storage-price-table-new1-north3",
      "#storage-files-date-storage-price-table-new1-east3",
      "#storage-files-date-storage-price-table-new2-north3",
      "#storage-files-date-storage-price-table-new2-east3",
      "#storage-files-data-storage-transactions-transfer-north3",
      "#storage-files-data-storage-transactions-transfer-east3",
      "#storage-files-date-storage-price-GZRS-table-china-north3",
      "#storage-files-date-storage-price-ZRS-table-china-north3",    
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"
    ]
  },
  {
    "os": "Storage Files",
    "region": "north-china2",
    "tableIDs": [
      "#storage-files-date-storage-price-table",
      "#storage-files-date-storage-price-table-new1s",
      "#storage-files-date-storage-price-table-new2s",
      "#storage-files-date-storage-price-table-new3s",
      "#storage-files-date-storage-price-table-new1-north3",
      "#storage-files-date-storage-price-table-new1-east3",
      "#storage-files-date-storage-price-table-new2-north3",
      "#storage-files-date-storage-price-table-new2-east3",
      "#storage-files-data-storage-transactions-transfer-north3",
      "#storage-files-data-storage-transactions-transfer-east3",
      "#storage-files-date-storage-price-GZRS-table-china-north3",
      "#storage-files-date-storage-price-ZRS-table-china-north3",      
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"
    ]
  },
  {
    "os": "Storage Files",
    "region": "east-china3",
    "tableIDs": [
      "#storage-files-date-storage-price-table",
      "#storage-files-date-storage-price-table-new1-east3",
      "#storage-files-date-storage-price-table-new1first",
      "#storage-files-date-storage-price-table-new1s",
      "#storage-files-date-storage-price-table-new2-east3",
      "#storage-files-date-storage-price-table-new2",
      "#storage-files-date-storage-price-table-new2s",
      "#storage-files-date-storage-price-table-new3",
      "#storage-files-date-storage-price-table-new3s",
      "#storage-files-data-storage-transactions-transfer",
      "#storage-files-date-storage-price-table-new4s",
      "#storage-files-data-storage-price-zrs-table",
      "#storage-files-data-storage-transactions-transfer-east3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-north3",
      "#storage-files-date-storage-price-ZRS-table-china-north3",
      "#storage-files-date-storage-price-GZRS-table-china-north3"
    ]
  },
  {
    "os": "Storage Files",
    "region": "north-china3",
    "tableIDs": [
      "#storage-files-date-storage-price-table",
      "#storage-files-date-storage-price-table-new1-east3",
      "#storage-files-date-storage-price-table-new1first",
      "#storage-files-date-storage-price-table-new1s",
      "#storage-files-date-storage-price-table-new2-east3",
      "#storage-files-date-storage-price-table-new2",
      "#storage-files-date-storage-price-table-new2s",
      "#storage-files-date-storage-price-table-new3",
      "#storage-files-date-storage-price-table-new3s",
      "#storage-files-data-storage-transactions-transfer",
      "#storage-files-date-storage-price-table-new4s",
      "#storage-files-data-storage-price-zrs-table",
      "#storage-files-data-storage-transactions-transfer-east3",
      "#storage-files-data-storage-transactions-transfer-ZRS_GZRS-east3"
    ]
  },
  {
    "os": "Anomaly Detector",
    "region": "east-china",
    "tableIDs": [
      "#Anomaly_Detector"
    ]
  },
  {
    "os": "Anomaly Detector",
    "region": "north-china",
    "tableIDs": [
      "#Anomaly_Detector"
    ]
  },
  {
    "os": "Anomaly Detector",
    "region": "north-china2",
    "tableIDs": [
      "#Anomaly_Detector"
    ]
  },
  {
    "os": "HD Insight",
    "region": "east-china2",
    "tableIDs": [
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table"
    ]
  },
  {
    "os": "HD Insight",
    "region": "north-china3",
    "tableIDs": [
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table-1",
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table-2",
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table-3"
    ]
  },
  {
    "os": "HD Insight",
    "region": "north-china",
    "tableIDs": [
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table",
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table-1"
    ]
  },
  {
    "os": "HD Insight",
    "region": "north-china2",
    "tableIDs": [
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table-1"
    ]
  },
  {
    "os": "HD Insight",
    "region": "east-china",
    "tableIDs": [
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table",
      "#hdinsight-computed-optimized-nodes-for-hdinsight-table-1"
    ]
  },
  {
    "os": "Key Vault",
    "region": "east-china3",
    "tableIDs": [
    ]
  },
  {
    "os": "Key Vault",
    "region": "north-china3",
    "tableIDs": [
    ]
  },
  {
    "os": "Key Vault",
    "region": "east-china2",
    "tableIDs": [
    ]
  },
  {
    "os": "Key Vault",
    "region": "north-china2",
    "tableIDs": [
    ]
  },
  {
    "os": "Key Vault",
    "region": "east-china",
    "tableIDs": [
      "#keyvault-vaults-N2E2N3E3-table",
      "#keyvault-key-protected-N2E2N3E3-table",
      "#keyvault-HSM-protected-N2E2N3E3-table",
      "#keyvault-key-rotation-N2E2N3E3-table"

    ]
  },
  {
    "os": "Key Vault",
    "region": "north-china",
    "tableIDs": [
      "#keyvault-vaults-N2E2N3E3-table",
      "#keyvault-key-protected-N2E2N3E3-table",
      "#keyvault-HSM-protected-N2E2N3E3-table",
      "#keyvault-key-rotation-N2E2N3E3-table"

    ]
  },
  {
    "os": "Azure_Data_Lake_Storage_Gen",
    "region": "north-china",
    "tableIDs": [
      "#Azure-Data-Lake-Storage-Gen1-1",
      "#Azure-Data-Lake-Storage-Gen1-2",
      "#Azure-Data-Lake-Storage-Gen1-3",
      "#Azure-Data-Lake-Storage-Gen1-4",
      "#Azure-Data-Lake-Storage-Gen2-1",
      "#Azure-Data-Lake-Storage-Gen2-2",
      "#Azure-Data-Lake-Storage-Gen2-3",
      "#Azure-Data-Lake-Storage-Gen2-4",
      "#Azure-Data-Lake-Storage-Gen1-1-north3",
      "#Azure-Data-Lake-Storage-Gen1-2-north3",
      "#Azure-Data-Lake-Storage-Gen1-3-north3",
      "#Azure-Data-Lake-Storage-Gen1-4-north3",
      "#Azure-Data-Lake-Storage-Gen2-1-north3",
      "#Azure-Data-Lake-Storage-Gen2-2-north3",
      "#Azure-Data-Lake-Storage-Gen2-3-north3",
      "#Azure-Data-Lake-Storage-Gen2-4-north3",
      "#Azure-Data-Lake-Storage-Gen1-1-east3",
      "#Azure-Data-Lake-Storage-Gen1-2-east3",
      "#Azure-Data-Lake-Storage-Gen1-3-east3",
      "#Azure-Data-Lake-Storage-Gen1-4-east3",
      "#Azure-Data-Lake-Storage-Gen2-1-east3",
      "#Azure-Data-Lake-Storage-Gen2-2-east3",
      "#Azure-Data-Lake-Storage-Gen2-3-east3",
      "#Azure-Data-Lake-Storage-Gen2-4-east3"
    ]
  },
  {
    "os": "Azure_Data_Lake_Storage_Gen",
    "region": "east-china",
    "tableIDs": [
      "#Azure-Data-Lake-Storage-Gen1-1",
      "#Azure-Data-Lake-Storage-Gen1-2",
      "#Azure-Data-Lake-Storage-Gen1-3",
      "#Azure-Data-Lake-Storage-Gen1-4",
      "#Azure-Data-Lake-Storage-Gen2-1",
      "#Azure-Data-Lake-Storage-Gen2-2",
      "#Azure-Data-Lake-Storage-Gen2-3",
      "#Azure-Data-Lake-Storage-Gen2-4",
      "#Azure-Data-Lake-Storage-Gen1-1-north3",
      "#Azure-Data-Lake-Storage-Gen1-2-north3",
      "#Azure-Data-Lake-Storage-Gen1-3-north3",
      "#Azure-Data-Lake-Storage-Gen1-4-north3",
      "#Azure-Data-Lake-Storage-Gen2-1-north3",
      "#Azure-Data-Lake-Storage-Gen2-2-north3",
      "#Azure-Data-Lake-Storage-Gen2-3-north3",
      "#Azure-Data-Lake-Storage-Gen2-4-north3",
      "#Azure-Data-Lake-Storage-Gen1-1-east3",
      "#Azure-Data-Lake-Storage-Gen1-2-east3",
      "#Azure-Data-Lake-Storage-Gen1-3-east3",
      "#Azure-Data-Lake-Storage-Gen1-4-east3",
      "#Azure-Data-Lake-Storage-Gen2-1-east3",
      "#Azure-Data-Lake-Storage-Gen2-2-east3",
      "#Azure-Data-Lake-Storage-Gen2-3-east3",
      "#Azure-Data-Lake-Storage-Gen2-4-east3"
    ]
  },
  {
    "os": "Azure_Data_Lake_Storage_Gen",
    "region": "north-china2",
    "tableIDs": [
      "#Azure-Data-Lake-Storage-Gen1-1-1",
      "#Azure-Data-Lake-Storage-Gen1-2-2",
      "#Azure-Data-Lake-Storage-Gen1-3-3",
      "#Azure-Data-Lake-Storage-Gen1-4-4",
      "#Azure-Data-Lake-Storage-Gen2-1-1",
      "#Azure-Data-Lake-Storage-Gen2-2-2",
      "#Azure-Data-Lake-Storage-Gen2-3-3",
      "#Azure-Data-Lake-Storage-Gen2-4-4",
      "#Azure-Data-Lake-Storage-Gen1-1-north3",
      "#Azure-Data-Lake-Storage-Gen1-2-north3",
      "#Azure-Data-Lake-Storage-Gen1-3-north3",
      "#Azure-Data-Lake-Storage-Gen1-4-north3",
      "#Azure-Data-Lake-Storage-Gen2-1-north3",
      "#Azure-Data-Lake-Storage-Gen2-2-north3",
      "#Azure-Data-Lake-Storage-Gen2-3-north3",
      "#Azure-Data-Lake-Storage-Gen2-4-north3",
      "#Azure-Data-Lake-Storage-Gen1-1-east3",
      "#Azure-Data-Lake-Storage-Gen1-2-east3",
      "#Azure-Data-Lake-Storage-Gen1-3-east3",
      "#Azure-Data-Lake-Storage-Gen1-4-east3",
      "#Azure-Data-Lake-Storage-Gen2-1-east3",
      "#Azure-Data-Lake-Storage-Gen2-2-east3",
      "#Azure-Data-Lake-Storage-Gen2-3-east3",
      "#Azure-Data-Lake-Storage-Gen2-4-east3"
    ]
  },
  {
    "os": "Azure_Data_Lake_Storage_Gen",
    "region": "east-china2",
    "tableIDs": [
      "#Azure-Data-Lake-Storage-Gen1-1-1",
      "#Azure-Data-Lake-Storage-Gen1-2-2",
      "#Azure-Data-Lake-Storage-Gen1-3-3",
      "#Azure-Data-Lake-Storage-Gen1-4-4",
      "#Azure-Data-Lake-Storage-Gen2-1-1",
      "#Azure-Data-Lake-Storage-Gen2-2-2",
      "#Azure-Data-Lake-Storage-Gen2-3-3",
      "#Azure-Data-Lake-Storage-Gen2-4-4",
      "#Azure-Data-Lake-Storage-Gen1-1-north3",
      "#Azure-Data-Lake-Storage-Gen1-2-north3",
      "#Azure-Data-Lake-Storage-Gen1-3-north3",
      "#Azure-Data-Lake-Storage-Gen1-4-north3",
      "#Azure-Data-Lake-Storage-Gen2-1-north3",
      "#Azure-Data-Lake-Storage-Gen2-2-north3",
      "#Azure-Data-Lake-Storage-Gen2-3-north3",
      "#Azure-Data-Lake-Storage-Gen2-4-north3",
      "#Azure-Data-Lake-Storage-Gen1-1-east3",
      "#Azure-Data-Lake-Storage-Gen1-2-east3",
      "#Azure-Data-Lake-Storage-Gen1-3-east3",
      "#Azure-Data-Lake-Storage-Gen1-4-east3",
      "#Azure-Data-Lake-Storage-Gen2-1-east3",
      "#Azure-Data-Lake-Storage-Gen2-2-east3",
      "#Azure-Data-Lake-Storage-Gen2-3-east3",
      "#Azure-Data-Lake-Storage-Gen2-4-east3"
    ]
  },
  {
    "os": "Azure_Data_Lake_Storage_Gen",
    "region": "north-china3",
    "tableIDs": [
      "#Azure-Data-Lake-Storage-Gen1-1-east3",
      "#Azure-Data-Lake-Storage-Gen1-2-east3",
      "#Azure-Data-Lake-Storage-Gen1-3-east3",
      "#Azure-Data-Lake-Storage-Gen1-4-east3",
      "#Azure-Data-Lake-Storage-Gen2-1-east3",
      "#Azure-Data-Lake-Storage-Gen2-2-east3",
      "#Azure-Data-Lake-Storage-Gen2-3-east3",
      "#Azure-Data-Lake-Storage-Gen2-4-east3",
      "#Azure-Data-Lake-Storage-Gen1-1-1",
      "#Azure-Data-Lake-Storage-Gen1-2-2",
      "#Azure-Data-Lake-Storage-Gen1-3-3",
      "#Azure-Data-Lake-Storage-Gen1-4-4",
      "#Azure-Data-Lake-Storage-Gen2-1-1",
      "#Azure-Data-Lake-Storage-Gen2-2-2",
      "#Azure-Data-Lake-Storage-Gen2-3-3",
      "#Azure-Data-Lake-Storage-Gen2-4-4",
      "#Azure-Data-Lake-Storage-Gen1-1",
      "#Azure-Data-Lake-Storage-Gen1-2",
      "#Azure-Data-Lake-Storage-Gen1-3",
      "#Azure-Data-Lake-Storage-Gen1-4",
      "#Azure-Data-Lake-Storage-Gen2-1",
      "#Azure-Data-Lake-Storage-Gen2-2",
      "#Azure-Data-Lake-Storage-Gen2-3",
      "#Azure-Data-Lake-Storage-Gen2-4"
    ]
  },
  {
    "os": "Azure_Data_Lake_Storage_Gen",
    "region": "east-china3",
    "tableIDs": [
      "#Azure-Data-Lake-Storage-Gen2-3-east3",
      "#Azure-Data-Lake-Storage-Gen2-4-north3",
      "#Azure-Data-Lake-Storage-Gen2-2-north3",
      "#Azure-Data-Lake-Storage-Gen1-1-1",
      "#Azure-Data-Lake-Storage-Gen1-2-2",
      "#Azure-Data-Lake-Storage-Gen1-3-3",
      "#Azure-Data-Lake-Storage-Gen1-4-4",
      "#Azure-Data-Lake-Storage-Gen2-1-1",
      "#Azure-Data-Lake-Storage-Gen2-2-2",
      "#Azure-Data-Lake-Storage-Gen2-3-3",
      "#Azure-Data-Lake-Storage-Gen2-4-4",
      "#Azure-Data-Lake-Storage-Gen1-1",
      "#Azure-Data-Lake-Storage-Gen1-2",
      "#Azure-Data-Lake-Storage-Gen1-3",
      "#Azure-Data-Lake-Storage-Gen1-4",
      "#Azure-Data-Lake-Storage-Gen2-1",
      "#Azure-Data-Lake-Storage-Gen2-2",
      "#Azure-Data-Lake-Storage-Gen2-3",
      "#Azure-Data-Lake-Storage-Gen2-4",
      "#Azure-Data-Lake-Storage-Gen2-1-north3",
      "#Azure-Data-Lake-Storage-Gen1-1-north3",
      "#Azure-Data-Lake-Storage-Gen1-3-north3",
      "#Azure-Data-Lake-Storage-Gen1-2-north3",
      "#Azure-Data-Lake-Storage-Gen1-4-north3"
    ]
  },
  {
    "os": "API Management",
    "region": "north-china",
    "tableIDs": [
      "#API-Management-preview",
      "#API-Management-gateway"
    ]
  },
  {
    "os": "API Management",
    "region": "east-china",
    "tableIDs": [
      "#API-Management-preview",
      "#API-Management-gateway"
    ]
  },
  {
    "os": "API Management",
    "region": "north-china2",
    "tableIDs": [
      "#API-Management-preview2"
    ]
  },
  {
    "os": "API Management",
    "region": "east-china2",
    "tableIDs": [
      "#API-Management-preview2"
    ]
  },
  {
    "os": "API Management",
    "region": "north-china3",
    "tableIDs": [
      "#API-Management-preview2"
    ]
  },
  {
    "os": "Azure Virtual",
    "region": "north-china",
    "tableIDs": [
      "#Azure_Virtual_Desktop1",
      "#Azure_Virtual_Desktop2",
      "#Azure_Virtual_Desktop3",
      "#Azure_Virtual_Desktop4"
    ]
  },
  {
    "os": "Azure Virtual",
    "region": "east-china",
    "tableIDs": [
      "#Azure_Virtual_Desktop1",
      "#Azure_Virtual_Desktop2",
      "#Azure_Virtual_Desktop3",
      "#Azure_Virtual_Desktop4"
    ]
  },
  {
    "os": "Azure Database for PostgreSQL",
    "region": "north-china",
    "tableIDs": [
      "#postgresql-flexible-burstable-burstable",
      "#postgresql-flexible-burstable-storage",
      "#postgresql-flexible-burstable-backstorage",
      "#postgresql-flexible-memory-edsv4",
      "#postgresql-flexible-general-dsv3",
      "postgresql-flexible-general-ddsv4",
      "#postgresql-flexible-general-ddsv5",
      "#postgresql-flexible-memory-edsv5",
      "#postgresql-flexible-general-storage",
      "#postgresql-flexible-general-backstorage",
      "#postgresql-flexible-memory-esv3",
      "#postgresql-flexible-memory-storage",
      "#postgresql-flexible-memory-backstorage",
      "#postgresql-flexible-general-ddsv5-east3"
    ]
  },
  {
    "os": "Azure Database for PostgreSQL",
    "region": "north-china2",
    "tableIDs": [
      "#postgresql-flexible-burstable-burstable",
      "#postgresql-flexible-burstable-storage",
      "#postgresql-flexible-burstable-backstorage",
      "#postgresql-flexible-general-dsv3",      
      "#postgresql-flexible-general-storage",
      "#postgresql-flexible-general-backstorage",
      "#postgresql-flexible-memory-esv3",
      "#postgresql-flexible-memory-storage",
      "#postgresql-flexible-memory-backstorage",
      "#postgresql-flexible-general-ddsv5",
      "#postgresql-flexible-memory-edsv5",
      "#postgresql-flexible-general-ddsv4",
      "#postgresql-flexible-memory-edsv4",
      "#postgresql-flexible-general-ddsv5-east3"
    ]
  },
  {
    "os": "Azure Database for PostgreSQL",
    "region": "north-china3",
    "tableIDs": [
      "#postgresql-flexible-general-ddsv5-east3"
    ]
  },
  
  {
    "os": "Azure Database for PostgreSQL",
    "region": "east-china2",
    "tableIDs": [
      "#postgresql-flexible-burstable-burstable",
    "#postgresql-flexible-burstable-storage",
    "#postgresql-flexible-burstable-backstorage",
    "#postgresql-flexible-general-dsv3",    
    "#postgresql-flexible-general-storage",
    "#postgresql-flexible-general-backstorage",
    "#postgresql-flexible-memory-esv3",
    "#postgresql-flexible-memory-storage",
    "#postgresql-flexible-memory-backstorage",
    "#postgresql-flexible-general-ddsv5",
    "#postgresql-flexible-memory-edsv5",
    "#postgresql-flexible-general-ddsv4",
    "#postgresql-flexible-memory-edsv4",
    "#postgresql-flexible-general-ddsv5-east3"
    ]
  },
  {
    "os": "Azure Database for PostgreSQL",
    "region": "east-china",
    "tableIDs": [
      "#postgresql-flexible-burstable-burstable",
    "#postgresql-flexible-burstable-storage",
    "#postgresql-flexible-burstable-backstorage",
    "#postgresql-flexible-memory-edsv4",
    "#postgresql-flexible-general-dsv3",
    "postgresql-flexible-general-ddsv4",
    "#postgresql-flexible-general-ddsv5",
    "#postgresql-flexible-memory-edsv5", 
    "#postgresql-flexible-general-storage",
    "#postgresql-flexible-general-backstorage",
    "#postgresql-flexible-memory-esv3",
    "#postgresql-flexible-memory-storage",
    "#postgresql-flexible-memory-backstorage",
    "#postgresql-flexible-general-ddsv5-east3"
  ]
  },
  {
    "os": "Azure Database for PostgreSQL",
    "region": "east-china3",
    "tableIDs": [
      "#postgresql-flexible-memory-esv3",
      "#postgresql-flexible-general-dsv3",
      "#postgresql-flexible-general-ddsv5"
    ]
  },

  
  {
    "os": "Azure Database for MySQL",
    "region": "north-china",
    "tableIDs": [
      "#Azure_Database_For_MySQL1",
      "#Azure_Database_For_MySQL2",
      "#Azure_Database_For_MySQL3",
      "#Azure_Database_For_MySQL4",
      "#Azure_Database_For_MySQL5",
      "#Azure_Database_For_MySQL6",
      "#Azure_Database_For_MySQL7",
      "#Azure_Database_For_MySQL8",
      "#Azure_Database_For_MySQL20",
      "#Azure_Database_For_MySQL18",
      "#Azure_Database_For_MySQL10",
      "#Azure_Database_For_MySQL11",
      "#Azure_Database_For_MySQL12",
      "#Azure_Database_For_MySQL14",
      "#Azure_Database_For_MySQL19",
      "#Azure_Database_For_MySQL21",
      "#Azure_Database_For_MySQL17",
      "#Azure_Database_For_MySQL22",
      "#Azure_Database_For_MySQL9",
      "#Azure_Database_For_MySQL13",
      "Azure_Database_For_MySQL30",
      "#Azure_Database_For_MySQL31",
      "#Azure_Database_For_MySQL32",
      "#Azure_Database_For_MySQL34",
      "#Azure_Database_For_MySQL35",
      "#Azure_Database_For_MySQL_IOPS",
      "#Azure_Database_For_MySQL_IOPS_East3"
      
    ]
  },
  {
    "os": "Azure Database for MySQL",
    "region": "north-china2",
    "tableIDs": [
      "#Azure_Database_For_MySQL8",
      "#Azure_Database_For_MySQL10",
      "#Azure_Database_For_MySQL12",
      "#Azure_Database_For_MySQL14",
      "#Azure_Database_For_MySQL7",
      "#Azure_Database_For_MySQL18",
      "#Azure_Database_For_MySQL16",
      "#Azure_Database_For_MySQL15",
      "#Azure_Database_For_MySQL20",
      "#Azure_Database_For_MySQL31",
      "#Azure_Database_For_MySQL32",
      "#Azure_Database_For_MySQL34",
      "#Azure_Database_For_MySQL35",
      "#Azure_Database_For_MySQL_IOPS"
    ]
  },
  {
    "os": "Azure Database for MySQL",
    "region": "north-china3",
    "tableIDs": [
      "#Azure_Database_For_MySQL6",
      "#Azure_Database_For_MySQL4",
      "#Azure_Database_For_MySQL19",
      "#Azure_Database_For_MySQL17",
      "#Azure_Database_For_MySQL9",
      "#Azure_Database_For_MySQL13",
      "#Azure_Database_For_MySQL30",
      "#Azure_Database_For_MySQL33",
      "#Azure_Database_For_MySQL36",
      "#Azure_Database_For_MySQL37",
      "#Azure_Database_For_MySQL38",
      "#Azure_Database_For_MySQL39",
      "#Azure_Database_For_MySQL40",
      "#Azure_Database_For_MySQL41",
      "#Azure_Database_For_MySQL42",
      "#Azure_Database_For_MySQL43",
      "#Azure_Database_For_MySQL44",
      "#Azure_Database_For_MySQL45",
      "#Azure_Database_For_MySQL32",
      "#Azure_Database_For_MySQL34",
      "#Azure_Database_For_MySQL31",
      "#Azure_Database_For_MySQL35",
      "#Azure_Database_For_MySQL_IOPS_East3"
    ]
  },
  {
    "os": "Azure Database for MySQL",
    "region": "east-china3",
    "tableIDs": [
      "#Azure_Database_For_MySQL6",
      "#Azure_Database_For_MySQL4",
      "#Azure_Database_For_MySQL19",
      "#Azure_Database_For_MySQL17",
      "#Azure_Database_For_MySQL9",
      "#Azure_Database_For_MySQL13",
      "#Azure_Database_For_MySQL30",
      "#Azure_Database_For_MySQL33",
      "#Azure_Database_For_MySQL36",
      "#Azure_Database_For_MySQL37",
      "#Azure_Database_For_MySQL38",
      "#Azure_Database_For_MySQL39",
      "#Azure_Database_For_MySQL40",
      "#Azure_Database_For_MySQL41",
      "#Azure_Database_For_MySQL42",
      "#Azure_Database_For_MySQL43",
      "#Azure_Database_For_MySQL44",
      "#Azure_Database_For_MySQL45",
      "#Azure_Database_For_MySQL32",
      "#Azure_Database_For_MySQL34",
      "#Azure_Database_For_MySQL31",
      "#Azure_Database_For_MySQL35",
      "#Azure_Database_For_MySQL_IOPS"
    ]
  },
  {
    "os": "Azure Database for MySQL",
    "region": "east-china",
    "tableIDs": [
      "#Azure_Database_For_MySQL1",
      "#Azure_Database_For_MySQL2",
      "#Azure_Database_For_MySQL3",
      "#Azure_Database_For_MySQL4",
      "#Azure_Database_For_MySQL5",
      "#Azure_Database_For_MySQL6",
      "#Azure_Database_For_MySQL7",
      "#Azure_Database_For_MySQL8",
      "#Azure_Database_For_MySQL20",
      "#Azure_Database_For_MySQL18",
      "#Azure_Database_For_MySQL10",
      "#Azure_Database_For_MySQL12",
      "#Azure_Database_For_MySQL14",
      "#Azure_Database_For_MySQL19",
      "#Azure_Database_For_MySQL21",
      "#Azure_Database_For_MySQL17",
      "#Azure_Database_For_MySQL22",
      "#Azure_Database_For_MySQL9",
      "#Azure_Database_For_MySQL13",
      "#Azure_Database_For_MySQL30",
      "#Azure_Database_For_MySQL31",
      "#Azure_Database_For_MySQL32",
      "#Azure_Database_For_MySQL34",
      "#Azure_Database_For_MySQL35",
      "#Azure_Database_For_MySQL_IOPS",
      "#Azure_Database_For_MySQL_IOPS_East3"
    ]
  },
  {
    "os": "Azure Database for MySQL",
    "region": "east-china2",
    "tableIDs": [
      "#Azure_Database_For_MySQL8",
      "#Azure_Database_For_MySQL10",
      "#Azure_Database_For_MySQL12",
      "#Azure_Database_For_MySQL14",
      "#Azure_Database_For_MySQL7",
      "#Azure_Database_For_MySQL18",
      "#Azure_Database_For_MySQL16",
      "#Azure_Database_For_MySQL15",
      "#Azure_Database_For_MySQL20",
      "#Azure_Database_For_MySQL31",
      "#Azure_Database_For_MySQL32",
      "#Azure_Database_For_MySQL34",
      "#Azure_Database_For_MySQL35",
      "#Azure_Database_For_MySQL_IOPS"
    ]
  },
  {
    "os": "databricks",
    "region": "east-china",
    "tableIDs": [
      "#databricks-data-analysis",
      "#databricks-data-analysis1",
      "#databricks-data-analysis2",
      "#databricks-data-analysis3",
      "#databricks-data-analysis4",
      "#databricks-data-analysis5",
      "#databricks-data-analysis6",
      "#databricks-data-analysis7",
      "#databricks-data-analysis8",
      "#databricks-data-analysis9",
      "#databricks-data-analysis10",
      "#databricks-data-analysis11",
      "#databricks-data-analysis12",
      "#databricks-data-analysis13",
      "#databricks-data-analysis14",
      "#databricks-data-analysis15",
      "#databricks-data-analysis16",
      "#databricks-data-analysis17",
      "#databricks-data-analysis18",
      "#databricks-data-analysis19",
      "#databricks-data-analysis20",
      "#databricks-data-analysis21",
      "#databricks-data-analysis22",
      "#databricks-data-analysis23",
      "#databricks-data-analysis24",
      "#databricks-data-analysisN-1",
      "#databricks-data-analysisN-2",
      "#databricks-data-analysisN-3",
      "#databricks-General-DSV2",
      "#databricks-General-Dv2",
      "#databricks-General-DSv3",
      "#databricks-Memory-DSv2",
      "#databricks-Memory-Dv2",
      "#databricks-Memory-Esv3",
      "#databricks-Compute-Fsv2",
      "#databricks-Compute-F",
      "#databricks-Compute-NCsv3",
      "#databricks-General-all-DSV2",
      "#databricks-General-all-Dv2",
      "#databricks-General-all-DSv3",
      "#databricks-Memory-all-DSv2",
      "#databricks-Memory-all-Dv2",
      "#databricks-Memory-all-Esv3",
      "#databricks-Compute-all-Fsv2",
      "#databricks-Compute-all-F",
      "#databricks-Compute-all-NCsv3",
      "#databricks-General-job-DSV2",
      "#databricks-General-job-Dv2",
      "#databricks-General-job-DSv3",
      "#databricks-Memory-job-DSv2",
      "#databricks-Memory-job-Dv2",
      "#databricks-Memory-job-Esv3",
      "#databricks-Compute-job-Fsv2",
      "#databricks-Compute-job-F",
      "#databricks-Compute-job-NCsv3",
      "#general-computation-memory-eadsv5",
      "#job-computation-memory-eadsv5",
      "#databricks-data-memory-eadsv5"
    ]
  },
  {
    "os": "databricks",
    "region": "north-china",
    "tableIDs": [
      "#databricks-data-analysis",
      "#databricks-data-analysis1",
      "#databricks-data-analysis2",
      "#databricks-data-analysis3",
      "#databricks-data-analysis4",
      "#databricks-data-analysis5",
      "#databricks-data-analysis6",
      "#databricks-data-analysis7",
      "#databricks-data-analysis8",
      "#databricks-data-analysis9",
      "#databricks-data-analysis10",
      "#databricks-data-analysis11",
      "#databricks-data-analysis12",
      "#databricks-data-analysis13",
      "#databricks-data-analysis14",
      "#databricks-data-analysis15",
      "#databricks-data-analysis16",
      "#databricks-data-analysis17",
      "#databricks-data-analysis18",
      "#databricks-data-analysis19",
      "#databricks-data-analysis20",
      "#databricks-data-analysis21",
      "#databricks-data-analysis22",
      "#databricks-data-analysis23",
      "#databricks-data-analysis24",
      "#databricks-data-analysisN-1",
      "#databricks-data-analysisN-2",
      "#databricks-data-analysisN-3",
      "#databricks-General-DSV2",
      "#databricks-General-Dv2",
      "#databricks-General-DSv3",
      "#databricks-Memory-DSv2",
      "#databricks-Memory-Dv2",
      "#databricks-Memory-Esv3",
      "#databricks-Compute-Fsv2",
      "#databricks-Compute-F",
      "#databricks-Compute-NCsv3",
      "#databricks-General-all-DSV2",
      "#databricks-General-all-Dv2",
      "#databricks-General-all-DSv3",
      "#databricks-Memory-all-DSv2",
      "#databricks-Memory-all-Dv2",
      "#databricks-Memory-all-Esv3",
      "#databricks-Compute-all-Fsv2",
      "#databricks-Compute-all-F",
      "#databricks-Compute-all-NCsv3",
      "#databricks-General-job-DSV2",
      "#databricks-General-job-Dv2",
      "#databricks-General-job-DSv3",
      "#databricks-Memory-job-DSv2",
      "#databricks-Memory-job-Dv2",
      "#databricks-Memory-job-Esv3",
      "#databricks-Compute-job-Fsv2",
      "#databricks-Compute-job-F",
      "#databricks-Compute-job-NCsv3",
      "#general-computation-memory-eadsv5",
      "#job-computation-memory-eadsv5",
      "#databricks-data-memory-eadsv5"
    ]
  },
  {
    "os": "databricks",
    "region": "north-china2",
    "tableIDs": [
      "#general-computation-memory-eadsv5",
      "#job-computation-memory-eadsv5",
      "#databricks-data-memory-eadsv5"
    ]
  },
  {
    "os": "databricks",
    "region": "east-china2",
    "tableIDs": [
      "#general-computation-memory-eadsv5",
      "#job-computation-memory-eadsv5",
      "#databricks-data-memory-eadsv5"
    ]
  },
  {
    "os": "Azure Form Recognizer",
    "region": "north-china",
    "tableIDs": [
      "#API_Azure_Form_Recognizer"
    ]
  },
  {
    "os": "Azure Form Recognizer",
    "region": "east-china",
    "tableIDs": [
      "#API_Azure_Form_Recognizer"
    ]
  },
  {
    "os": "Azure Form Recognizer",
    "region": "north-china2",
    "tableIDs": []
  },
  {
    "os": "Azure Form Recognizer",
    "region": "north-china3",
    "tableIDs": []
  },
  {
    "os": "Azure Form Recognizer",
    "region": "east-china2",
    "tableIDs": []
  },
  {
    "os": "Azure IoT",
    "region": "north-china",
    "tableIDs": [
      "#azure-iot-standard-north3",
      "#azure-iot-basic-north3"
    ]
  },
  {
    "os": "Azure IoT",
    "region": "east-china",
    "tableIDs": [
      "#azure-iot-standard-north3",
      "#azure-iot-basic-north3"
    ]
  },
  {
    "os": "Azure IoT",
    "region": "north-china2",
    "tableIDs": [
      "#azure-iot-standard-north3",
      "#azure-iot-basic-north3"
    ]
  },
  {
    "os": "Azure IoT",
    "region": "east-china2",
    "tableIDs": [
      "#azure-iot-standard-north3",
      "#azure-iot-basic-north3"
    ]
  },
  {
    "os": "Azure IoT",
    "region": "north-china3",
    "tableIDs": [
      "#azure-iot-standard-elsearea",
      "#azure-iot-basic-elsearea"
    ]
  },
  {
    "os": "Azure IoT",
    "region": "east-china3",
    "tableIDs": [
      "#azure-iot-standard-elsearea",
      "#azure-iot-basic-elsearea"
    ]
  },
  {
    "os": "azure-firewall",
    "region": "north-china",
    "tableIDs": [
      
    ]
  },
  {
    "os": "azure-firewall",
    "region": "north-china2",
    "tableIDs": [
      "#azure_firewall_standard3"
    ]
  },
  {
    "os": "azure-firewall",
    "region": "east-china2",
    "tableIDs": [
      "#azure_firewall_standard3"
    ]
  },
  {
    "os": "azure-firewall",
    "region": "east-china",
    "tableIDs": [
   
    ]
  },
  {
    "os": "azure-firewall",
    "region": "north-china2",
    "tableIDs": [
      "#azure_firewall_premium"
    ]
  },
  {
    "os": "azure-firewall",
    "region": "east-china2",
    "tableIDs": [
      "#azure_firewall_premium"
    ]
  },
  {
    "os": "azure-firewall",
    "region": "north-china3",
    "tableIDs": [
      "#azure_firewall_standard2"
    ]
  },  
  {
    "os": "metrics-advisor",
    "region": "north-china",
    "tableIDs": [
      "#metrics_advisor_Desktop1"
    ]
  },
  {
    "os": "metrics-advisor",
    "region": "east-china",
    "tableIDs": [
      "#metrics_advisor_Desktop1"
    ]
  },
  {
    "os": "mysql",
    "region": "east-china3",
    "tableIDs": [
      "#MYSQL_gen51",
      "#MYSQL_gen5",
      "#mysql_Backup1",
      "#mysql_Backup",
      "#mysql_Burstable_Compute",
      "#1MYSQL_gen5"
    ]
  },
  {
    "os": "mysql",
    "region": "east-china2",
    "tableIDs": [
      "#MYSQL_gen51",
      "#MYSQL_gen52",
      "#mysql_Backup1",
      "#mysql_Backup2",
      "#mysql_Burstable_Compute",
      "#1MYSQL_gen51"
    ]
  },
  {
    "os": "mysql",
    "region": "east-china",
    "tableIDs": [
      "#MYSQL_gen51",
      "#MYSQL_gen52",
      "#mysql_Backup1",
      "#mysql_Backup2",
      "#mysql_Burstable_Compute1"
    ]
  },
  {
    "os": "mysql",
    "region": "north-china3",
    "tableIDs": [
      "#MYSQL_gen5",
      "#MYSQL_gen52",
      "#mysql_Backup",
      "#mysql_Backup2",
      "#mysql_Burstable_Compute1"
    ]
  },
  {
    "os": "mysql",
    "region": "north-china2",
    "tableIDs": [
      "#MYSQL_gen51",
      "#MYSQL_gen52",
      "#mysql_Backup1",
      "#mysql_Backup2",
      "#mysql_Burstable_Compute1"
    ]
  },
  {
    "os": "mysql",
    "region": "north-china",
    "tableIDs": [
      "#MYSQL_gen51",
      "#MYSQL_gen52",
      "#mysql_Backup1",
      "#mysql_Backup2",
      "#mysql_Burstable_Compute1"
    ]
  },
  {
    "os": "Queues Storage",
    "region": "east-china3",
    "tableIDs": [
      "#queues-storage-data-v2",
      "#queues-storage-data-v2-north3",
      "#queues-storage-ops-v2",
      "#queues-storage-ops-v2-north3",
      "#queues-storage-data-v1",
      "#queues-storage-data-v1-north3",
      "#queues-storage-ops-v1",
      "#queues-storage-ops-v1-north3"
    ]
  },
  {
    "os": "Queues Storage",
    "region": "east-china2",
    "tableIDs": [
      "#queues-storage-data-v2-east3",
      "#queues-storage-data-v2-north3",
      "#queues-storage-ops-v2-east3",
      "#queues-storage-ops-v2-north3",
      "#queues-storage-data-v1-east3",
      "#queues-storage-data-v1-north3",
      "#queues-storage-ops-v1-east3",
      "#queues-storage-ops-v1-north3"
    ]
  },
  {
    "os": "Queues Storage",
    "region": "east-china",
    "tableIDs": [
      "#queues-storage-data-v2-east3",
      "#queues-storage-data-v2-north3",
      "#queues-storage-ops-v2-east3",
      "#queues-storage-ops-v2-north3",
      "#queues-storage-data-v1-east3",
      "#queues-storage-data-v1-north3",
      "#queues-storage-ops-v1-east3",
      "#queues-storage-ops-v1-north3"
    ]
  },
  {
    "os": "Queues Storage",
    "region": "north-china3",
    "tableIDs": [
      "#queues-storage-data-v2",
      "#queues-storage-data-v2-east3",
      "#queues-storage-ops-v2",
      "#queues-storage-ops-v2-east3",
      "#queues-storage-data-v1",
      "#queues-storage-data-v1-east3",
      "#queues-storage-ops-v1",
      "#queues-storage-ops-v1-east3"
    ]
  },
  {
    "os": "Queues Storage",
    "region": "north-china2",
    "tableIDs": [
      "#queues-storage-data-v2-east3",
      "#queues-storage-data-v2-north3",
      "#queues-storage-ops-v2-east3",
      "#queues-storage-ops-v2-north3",
      "#queues-storage-data-v1-east3",
      "#queues-storage-data-v1-north3",
      "#queues-storage-ops-v1-east3",
      "#queues-storage-ops-v1-north3"
    ]
  },
  {
    "os": "Queues Storage",
    "region": "north-china",
    "tableIDs": [
      "#queues-storage-data-v2-east3",
      "#queues-storage-data-v2-north3",
      "#queues-storage-ops-v2-east3",
      "#queues-storage-ops-v2-north3",
      "#queues-storage-data-v1-east3",
      "#queues-storage-data-v1-north3",
      "#queues-storage-ops-v1-east3",
      "#queues-storage-ops-v1-north3"
    ]
  },
  {
    "os": "Page Blobs",
    "region": "east-china3",
    "tableIDs": [
      "#page-blobs-gv2-premium-addition-n3",
      "#page-blobs-gv1-premium",
      "#page-blobs-gv1-premium-addition",
      "#page-blobs-gv1-standard-data-storage-east3",
      "#page-blobs-gv1-standard-data-storage",
      "#page-blobs-gv1-unmanaged-disks-east3",
      "#page-blobs-gv1-unmanaged-disks",
      "#page-blobs-gv1-standard-non-disk-east3",
      "#page-blobs-gv1-standard-non-disk",
      "#page-blobs-gv2-premium",
      "#page-blobs-gv2-standard-data-storage-N3"
    ]
  },
  {
    "os": "Page Blobs",
    "region": "north-china3",
    "tableIDs": [
      "#page-blobs-gv2-premium-addition",
      "#page-blobs-gv1-premium-east3",
      "#page-blobs-gv1-premium-addition",
      "#page-blobs-gv1-standard-data-storage-east3",
      "#page-blobs-gv1-standard-data-storage",
      "#page-blobs-gv1-unmanaged-disks-east3",
      "#page-blobs-gv1-unmanaged-disks",
      "#page-blobs-gv1-standard-non-disk-east3",
      "#page-blobs-gv1-standard-non-disk",
      "#page-blobs-gv2-premium",
      "#page-blobs-gv2-standard-data-storage"
    ]
  },
  {
    "os": "Page Blobs",
    "region": "east-china2",
    "tableIDs": [
      "#page-blobs-gv2-premium-addition-n3",
      "#page-blobs-gv1-premium-east3",
      "#page-blobs-gv1-standard-data-storage-north3",
      "#page-blobs-gv1-standard-data-storage-east3",
      "#page-blobs-gv1-unmanaged-disks-east3",
      "#page-blobs-gv1-unmanaged-disks-north3",
      "#page-blobs-gv1-standard-non-disk-north3",
      "#page-blobs-gv1-standard-non-disk-east3",
      "#page-blobs-gv2-premium",
      "#page-blobs-gv2-standard-data-storage-N3"
    ]
  },
  {
    "os": "Page Blobs",
    "region": "north-china2",
    "tableIDs": [
      "#page-blobs-gv2-premium-addition-n3",
      "#page-blobs-gv1-premium-east3",
      "#page-blobs-gv1-standard-data-storage-north3",
      "#page-blobs-gv1-standard-data-storage-east3",
      "#page-blobs-gv1-unmanaged-disks-east3",
      "#page-blobs-gv1-unmanaged-disks-north3",
      "#page-blobs-gv1-standard-non-disk-north3",
      "#page-blobs-gv1-standard-non-disk-east3",
      "#page-blobs-gv2-premium",
      "#page-blobs-gv2-standard-data-storage-N3"
    ]
  },
  {
    "os": "Page Blobs",
    "region": "east-china",
    "tableIDs": [
      "#page-blobs-gv2-premium-addition-n3",
      "#page-blobs-gv1-premium-east3",
      "#page-blobs-gv1-standard-data-storage-north3",
      "#page-blobs-gv1-standard-data-storage-east3",
      "#page-blobs-gv1-unmanaged-disks-east3",
      "#page-blobs-gv1-unmanaged-disks-north3",
      "#page-blobs-gv1-standard-non-disk-north3",
      "#page-blobs-gv1-standard-non-disk-east3",
      "#page-blobs-gv2-premium",
      "#page-blobs-gv2-standard-data-storage-N3"
    ]
  },
  {
    "os": "Page Blobs",
    "region": "north-china",
    "tableIDs": [
      "#page-blobs-gv2-premium-addition-n3",
      "#page-blobs-gv1-premium-east3",
      "#page-blobs-gv1-standard-data-storage-north3",
      "#page-blobs-gv1-standard-data-storage-east3",
      "#page-blobs-gv1-unmanaged-disks-east3",
      "#page-blobs-gv1-unmanaged-disks-north3",
      "#page-blobs-gv1-standard-non-disk-north3",
      "#page-blobs-gv1-standard-non-disk-east3",
      "#page-blobs-gv2-premium",
      "#page-blobs-gv2-standard-data-storage-N3"
    ]
  },
  {
    "os": "Tables",
    "region": "east-china3",
    "tableIDs": [
     "#tables-data-storage-east3",
      "#tables-data-storage",
      "#tables-operations-transfer-north3",
      "#tables-operations-transfer"
    ]
  },
  {
    "os": "Tables",
    "region": "north-china3",
    "tableIDs": [
      "#tables-data-storage-east3",
      "#tables-data-storage",
      "#tables-operations-transfer-east3",
      "#tables-operations-transfer"
    ]
  },
  {
    "os": "Tables",
    "region": "east-china2",
    "tableIDs": [
      "#tables-data-storage-north3",
      "#tables-data-storage-east3",
      "#tables-operations-transfer-north3",
      "#tables-operations-transfer-east3"
    ]
  },
  {
    "os": "Tables",
    "region": "north-china2",
    "tableIDs": [
      "#tables-data-storage-north3",
      "#tables-data-storage-east3",
      "#tables-operations-transfer-north3",
      "#tables-operations-transfer-east3"
    ]
  },
  {
    "os": "Tables",
    "region": "east-china",
    "tableIDs": [
      "#tables-data-storage-north3",
      "#tables-data-storage-east3",
      "#tables-operations-transfer-north3",
      "#tables-operations-transfer-east3"
    ]
  },
  {
    "os": "Tables",
    "region": "north-china",
    "tableIDs": [
      "#tables-data-storage-north3",
      "#tables-data-storage-east3",
      "#tables-operations-transfer-north3",
      "#tables-operations-transfer-east3"
    ]
  },
  {
    "os": "Azure Cosmos DB",
    "region": "east-china3",
    "tableIDs": [
      "#cosmos-db-3",
      "#cosmos-db-2",
      "#cosmos-db-1",
      "#cosmos-3",
      "#cosmos-7",
      "#cosmos-11",
      "#cosmos-3-2",
      "#cosmos-3-3"
    ]
  },
  {
    "os": "Azure Cosmos DB",
    "region": "east-china2",
    "tableIDs": [
      "#cosmos-db-3-n3",
      "#cosmos-db-2-n3",
      "#cosmos-db-1-n3",
      "#cosmos-db-s-n3",
      "#cosmos-11-3",
      "#cosmos-11-2",
      "#cosmos-11-1",
      "#cosmos-11-4"
    ]
  },
  {
    "os": "Azure Cosmos DB",
    "region": "east-china",
    "tableIDs": [
      "#cosmos-db-3-n3",
      "#cosmos-db-2-n3",
      "#cosmos-db-1-n3",
      "#cosmos-db-s-n3",
      "#cosmos-11-3",
      "#cosmos-11-2",
      "#cosmos-11-1",
      "#cosmos-11-4"
    ]
  },
  {
    "os": "Azure Cosmos DB",
    "region": "north-china3",
    "tableIDs": [
      "#cosmos-db-3",
      "#cosmos-db-2",
      "#cosmos-db-1",
      "#cosmos-3",
      "#cosmos-7",
      "#cosmos-11",
      "#cosmos-3-2",
      "#cosmos-3-3"
    ]
  },
  {
    "os": "Azure Cosmos DB",
    "region": "north-china2",
    "tableIDs": [
      "#cosmos-db-3-n3",
      "#cosmos-db-2-n3",
      "#cosmos-db-1-n3",
      "#cosmos-db-s-n3",
      "#cosmos-11-3",
      "#cosmos-11-2",
      "#cosmos-11-1",
      "#cosmos-11-4"
    ]
  },
  {
    "os": "Azure Cosmos DB",
    "region": "north-china",
    "tableIDs": [
      "#cosmos-db-3-n3",
      "#cosmos-db-2-n3",
      "#cosmos-db-1-n3",
      "#cosmos-db-s-n3",
      "#cosmos-11-3",
      "#cosmos-11-2",
      "#cosmos-11-1",
      "#cosmos-11-4"
    ]
  },
  {
    "os": "Azure Cache for Redis",
    "region": "east-china",
    "tableIDs": [
      "#cache6",
      "#cache4",
      "#cache5"
    ]
  },
  {
    "os": "Azure Cache for Redis",
    "region": "east-china2",
    "tableIDs": [
      "#cache3",
      "#cache4",
      "#cache5"
    ]
  },
  {
    "os": "Azure Cache for Redis",
    "region": "north-china",
    "tableIDs": [
      "#cache6",
      "#cache4",
      "#cache5"
    ]
  },
  {
    "os": "Azure Cache for Redis",
    "region": "north-china2",
    "tableIDs": [
      "#cache3",
      "#cache4",
      "#cache5"
    ]
  },
  {
    "os": "Azure Cache for Redis",
    "region": "north-china3",
    "tableIDs": [
      "#cache3"
    ]
  },
  {
    "os": "azure-monitor",
    "region": "north-china",
    "tableIDs": [
      "#monitor-basic-log",
      "#monitor-log-data-archive",
      "#monitor-export-n3e3"
    ]
  },
  {
    "os": "azure-monitor",
    "region": "east-china",
    "tableIDs": [
      "#monitor-basic-log",
      "#monitor-log-data-archive",
      "#monitor-export-n3e3"
    ]
  },
  {
    "os": "azure-monitor",
    "region": "north-china2",
    "tableIDs": [
      "#monitor-export-n3e3"
    ]
  },
  {
    "os": "azure-monitor",
    "region": "east-china2",
    "tableIDs": [
      "#monitor-export-n3e3"
    ]
  },
  {
    "os": "azure-monitor",
    "region": "north-china3",
    "tableIDs": [
    ]
  },
  {
    "os": "azure-monitor",
    "region": "east-china3",
    "tableIDs": [
    ]
  },
  
  {
    "os":"VPN Gateway",
     "region":"east-china",
     "tableIDs":[
      "#VPN_Gateway_table_Availability_Zones"
    ]
  },
  {
    "os":"VPN Gateway",
     "region":"east-china2",
     "tableIDs":[
      "#VPN_Gateway_table_Availability_Zones"
    ]
  },
  {
    "os":"VPN Gateway",
     "region":"north-china2",
     "tableIDs":[
      "#VPN_Gateway_table_Availability_Zones"
    ]
  },
  {
    "os":"VPN Gateway",
     "region":"north-china",
     "tableIDs":[
      "#VPN_Gateway_table_Availability_Zones"
    ]
  }
]